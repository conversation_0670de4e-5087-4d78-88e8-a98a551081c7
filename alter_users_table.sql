-- SQL script to alter the users table for enhanced registration

-- Add email verification fields
ALTER TABLE users
ADD COLUMN email_verified TINYINT(1) DEFAULT 0 AFTER email,
ADD COLUMN email_verification_token VARCHAR(255) NULL AFTER email_verified,
ADD COLUMN email_verification_sent_at TIMESTAMP NULL AFTER email_verification_token;

-- Add terms acceptance fields
ALTER TABLE users
ADD COLUMN terms_accepted TINYINT(1) DEFAULT 0,
ADD COLUMN terms_accepted_at TIMESTAMP NULL;

-- Add additional profile fields
ALTER TABLE users
ADD COLUMN full_name VARCHAR(255) NULL,
ADD COLUMN country VARCHAR(100) NULL,
ADD COLUMN phone VARCHAR(50) NULL,
ADD COLUMN profile_image VARCHAR(500) NULL,
ADD COLUMN profile_completed TINYINT(1) DEFAULT 0;

-- Add security fields
ALTER TABLE users
ADD COLUMN last_password_change TIMESTAMP NULL,
ADD COLUMN failed_login_attempts INT DEFAULT 0,
ADD COLUMN account_locked TINYINT(1) DEFAULT 0,
ADD COLUMN account_locked_until TIMESTAMP NULL;
ADD COLUMN avatar_id INT NULL;


-- Add comments to explain the table structure
-- email_verified: Whether the user's email has been verified
-- email_verification_token: Token used for email verification
-- email_verification_sent_at: When the verification email was sent
-- terms_accepted: Whether the user has accepted the terms of service
-- terms_accepted_at: When the user accepted the terms of service
-- full_name: User's full name
-- country: User's country
-- phone: User's phone number
-- profile_completed: Whether the user has completed their profile
-- last_password_change: When the user last changed their password
-- failed_login_attempts: Number of consecutive failed login attempts
-- account_locked: Whether the account is locked due to too many failed login attempts
-- account_locked_until: When the account lock expires
