<?php

require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/utils.php';
require_once dirname(__DIR__) . '/models/ResultModel.php';

/**
 * Upload profile image for authenticated user
 *
 * @return void
 */
function upload_profile_image()
{
    global $link;

    // JWT token'dan user ID'yi al
    $userId = authenticate_user();
    if (!$userId) {
        $response = new ErrorResult('Authentication required');
        $response->send(401);
        return;
    }

    // Dosya yükleme kontrolü
    if (!isset($_FILES['profile_image']) || $_FILES['profile_image']['error'] !== UPLOAD_ERR_OK) {
        $response = new ErrorResult('No file uploaded or upload error occurred');
        $response->send(400);
        return;
    }

    $file = $_FILES['profile_image'];

    // Dosya boyutu kontrolü (5MB maksimum)
    $maxFileSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxFileSize) {
        $response = new ErrorResult('File size too large. Maximum 5MB allowed');
        $response->send(400);
        return;
    }

    // Dosya türü kontrolü
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = mime_content_type($file['tmp_name']);

    if (!in_array($fileType, $allowedTypes)) {
        $response = new ErrorResult('Invalid file type. Only JPEG, PNG, GIF and WebP images are allowed');
        $response->send(400);
        return;
    }

    // Dosya uzantısı kontrolü
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($fileExtension, $allowedExtensions)) {
        $response = new ErrorResult('Invalid file extension');
        $response->send(400);
        return;
    }

    // Upload klasörü oluşturma
    $ds = DIRECTORY_SEPARATOR;
    $uploadDir = dirname(__DIR__) . $ds . 'uploads' . $ds . 'profile_images' . $ds;

    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            $response = new ErrorResult('Failed to create upload directory');
            $response->send(500);
            return;
        }
    }

    // Benzersiz dosya adı oluşturma
    $fileName = 'profile_' . $userId . '_' . time() . '.' . $fileExtension;
    $targetPath = $uploadDir . $fileName;

    // Eski profil fotoğrafını silme
    $oldImageQuery = "SELECT profile_image FROM users WHERE id = ?";
    $oldImageStmt = mysqli_prepare($link, $oldImageQuery);
    mysqli_stmt_bind_param($oldImageStmt, "i", $userId);
    mysqli_stmt_execute($oldImageStmt);
    $oldImageResult = mysqli_stmt_get_result($oldImageStmt);
    $oldImageData = mysqli_fetch_assoc($oldImageResult);

    if ($oldImageData && $oldImageData['profile_image']) {
        $oldImagePath = dirname(__DIR__) . $ds . $oldImageData['profile_image'];
        if (file_exists($oldImagePath)) {
            unlink($oldImagePath);
        }
    }

    // Dosyayı taşıma
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        // Veritabanında profil fotoğrafı yolunu güncelleme
        $relativePath = 'uploads' . $ds . 'profile_images' . $ds . $fileName;
        $updateQuery = "UPDATE users SET profile_image = ? WHERE id = ?";
        $updateStmt = mysqli_prepare($link, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, "si", $relativePath, $userId);

        if (mysqli_stmt_execute($updateStmt)) {
            $imageUrl = 'uploads/profile_images/' . $fileName;
            $response = new SuccessResult([
                'message' => 'Profile image uploaded successfully',
                'image_url' => $imageUrl,
                'file_name' => $fileName
            ]);
            $response->send();
        } else {
            // Dosyayı sil çünkü veritabanı güncellenemedi
            unlink($targetPath);
            $response = new ErrorResult('Failed to update database');
            $response->send(500);
        }
    } else {
        $response = new ErrorResult('Failed to upload file');
        $response->send(500);
    }
}

/**
 * Get user profile image URL
 *
 * @return void
 */
function get_profile_image()
{
    global $link;

    // JWT token'dan user ID'yi al
    $userId = authenticate_user();
    if (!$userId) {
        $response = new ErrorResult('Authentication required');
        $response->send(401);
        return;
    }

    $query = "SELECT profile_image FROM users WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "i", $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user = mysqli_fetch_assoc($result);

    if ($user) {
        $imageUrl = $user['profile_image'] ? str_replace('\\', '/', $user['profile_image']) : null;
        $response = new SuccessResult([
            'profile_image' => $imageUrl
        ]);
        $response->send();
    } else {
        $response = new ErrorResult('User not found');
        $response->send(404);
    }
}

/**
 * Delete user profile image
 *
 * @return void
 */
function delete_profile_image()
{
    global $link;

    // JWT token'dan user ID'yi al
    $userId = authenticate_user();
    if (!$userId) {
        $response = new ErrorResult('Authentication required');
        $response->send(401);
        return;
    }

    // Mevcut profil fotoğrafını al
    $query = "SELECT profile_image FROM users WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "i", $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user = mysqli_fetch_assoc($result);

    if ($user && $user['profile_image']) {
        $ds = DIRECTORY_SEPARATOR;
        $imagePath = dirname(__DIR__) . $ds . $user['profile_image'];

        // Dosyayı sil
        if (file_exists($imagePath)) {
            unlink($imagePath);
        }

        // Veritabanından profil fotoğrafını kaldır
        $updateQuery = "UPDATE users SET profile_image = NULL WHERE id = ?";
        $updateStmt = mysqli_prepare($link, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, "i", $userId);

        if (mysqli_stmt_execute($updateStmt)) {
            $response = new SuccessResult('Profile image deleted successfully');
            $response->send();
        } else {
            $response = new ErrorResult('Failed to update database');
            $response->send(500);
        }
    } else {
        $response = new ErrorResult('No profile image found');
        $response->send(404);
    }
}

?>
