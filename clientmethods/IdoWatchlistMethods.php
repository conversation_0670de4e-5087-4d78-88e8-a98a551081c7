<?php

/**
 * IDO Watchlist Methods
 * Contains methods for managing IDO watchlists
 */

/**
 * Add an IDO project to a watchlist
 *
 * @param int $watchlist_id The ID of the watchlist
 * @param int $ido_id The ID of the IDO project
 * @return void
 */
function add_to_ido_watchlist($watchlist_id, $ido_id)
{
    global $link;
    $watchlist_id = intval($watchlist_id);
    $ido_id = intval($ido_id);
    $insert_query = "INSERT INTO ido_watchlist_projects (watchlist_id, ido_id) VALUES ($watchlist_id, $ido_id)";
    $insert_result = mysqli_query($link, $insert_query);
    if ($insert_result) {
        $response = new SuccessResult("IDO project successfully added to watchlist.");
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to add IDO project to watchlist. Error: $error");
        $response->send(500);
    }
}

/**
 * Remove an IDO project from a watchlist
 *
 * @param int $watchlist_id The ID of the watchlist
 * @param int $ido_id The ID of the IDO project
 * @return void
 */
function remove_from_ido_watchlist($watchlist_id, $ido_id)
{
    global $link;
    $watchlist_id = intval($watchlist_id);
    $ido_id = intval($ido_id);
    $delete_query = "DELETE FROM ido_watchlist_projects WHERE watchlist_id = $watchlist_id AND ido_id = $ido_id";
    $delete_result = mysqli_query($link, $delete_query);
    if ($delete_result) {
        $response = new SuccessResult("IDO project successfully removed from watchlist.");
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to remove IDO project from watchlist. Error: $error");
        $response->send(500);
    }
}

/**
 * Create a new IDO watchlist
 *
 * @param int $user_id The ID of the user
 * @param string $watchlist_name The name of the watchlist
 * @param int $iconid The ID of the icon
 * @param int|null $ido_id Optional ID of an IDO project to add to the watchlist
 * @param string|null $description Optional description of the watchlist
 * @return void
 */
function add_new_ido_watchlist($user_id, $watchlist_name, $iconid, $ido_id = null, $description = null)
{
    global $link;
    $user_id = intval($user_id);
    // İzin kontrolü - Kullanıcının IDO watchlist ekleyip ekleyemeyeceğini kontrol et
    if (!hasUserPermission($user_id, 'can_add_ido_watchlists')) {
        global $link;
        // Kullanıcının abonelik seviyesini güvenli şekilde al (Stripe subscription status ile doğrula)
        $query = "SELECT u.subscription_level, s.status as stripe_status
                  FROM users u
                  LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                      AND s.status IN ('active', 'trialing')
                  WHERE u.id = ?
                  ORDER BY s.created_at DESC
                  LIMIT 1";
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        $subscriptionLevel = $user['subscription_level'] ?? 'free';
        // Bir sonraki seviyeyi belirle
        $nextLevel = '';
        if ($subscriptionLevel === 'free' || $subscriptionLevel === 'basic') {
            $nextLevel = 'Advance';
        }
        $errorMessage = "IDO watchlist creation requires Advance or Premium subscription. Please upgrade to $nextLevel.";
        $response = new ErrorResult($errorMessage);
        $response->send(403);
        return;
    }
    // Kullanıcının daha fazla watchlist ekleyip ekleyemeyeceğini kontrol et
    if (!canUserAddMoreWatchlists($user_id)) {
        global $link;
        // Kullanıcının abonelik seviyesini güvenli şekilde al (Stripe subscription status ile doğrula)
        $query = "SELECT u.subscription_level, s.status as stripe_status
                  FROM users u
                  LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                      AND s.status IN ('active', 'trialing')
                  WHERE u.id = ?
                  ORDER BY s.created_at DESC
                  LIMIT 1";
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
        $stripeStatus = $user['stripe_status'];

        // Security check: Only allow paid features if Stripe subscription is active
        $subscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';
        // Kullanıcının mevcut watchlist sayısını al
        $query = "SELECT COUNT(*) as count FROM ido_watchlists WHERE user_id = ?";
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        $currentCount = $row['count'];
        // Abonelik seviyesine göre limit belirle
        $limit = 0;
        $nextLevel = '';
        if ($subscriptionLevel === 'advance') {
            $limit = 10;
            $nextLevel = 'Premium';
        }
        $errorMessage = "You have reached your IDO watchlist limit ($currentCount/$limit). Please upgrade to $nextLevel for unlimited watchlists.";
        $response = new ErrorResult($errorMessage);
        $response->send(403);
        return;
    }
    $watchlist_name = mysqli_real_escape_string($link, $watchlist_name);
    // description NULL kontrolü
    // Eğer description null, undefined veya boş string ise NULL olarak ayarla
    if ($description === null || $description === '' || trim($description) === '') {
        $description = "NULL";
    } else {
        $description = "'" . mysqli_real_escape_string($link, $description) . "'";
    }
    // ido_id NULL kontrolü
    $ido_id = isset($ido_id) ? intval($ido_id) : "NULL";
    $check_query = "SELECT * FROM ido_watchlists WHERE user_id = $user_id AND name = '$watchlist_name'";
    $check_result = mysqli_query($link, $check_query);
    if ($check_result && mysqli_num_rows($check_result) > 0) {
        $response = new ErrorResult("An IDO watchlist with this name already exists for the user.");
        $response->send(400);
        return;
    }
    // Yeni watchlist ekle
    $insert_query = "INSERT INTO ido_watchlists (user_id, name, icon_id, description) VALUES ($user_id, '$watchlist_name', $iconid, $description)";
    $insert_result = mysqli_query($link, $insert_query);
    if ($insert_result) {
        $watchlist_id = mysqli_insert_id($link); // Yeni eklenen watchlist'in ID'sini al
        // ido_id varsa ido_watchlist_projects tablosuna ekleme yap
        if ($ido_id !== "NULL") {
            $ido_insert_query = "INSERT INTO ido_watchlist_projects (watchlist_id, ido_id) VALUES ($watchlist_id, $ido_id)";
            $ido_insert_result = mysqli_query($link, $ido_insert_query);
            if (!$ido_insert_result) {
                $error = mysqli_error($link);
                $response = new ErrorResult("IDO watchlist created but failed to add IDO project. Error: $error");
                $response->send(500);
                return;
            }
        }
        $response = new SuccessResult("IDO watchlist successfully added.");
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to add new IDO watchlist. Error: $error");
        $response->send(500);
    }
}

/**
 * Delete an IDO watchlist
 *
 * @param int $watchlist_id The ID of the watchlist
 * @param int $user_id The ID of the user
 * @return void
 */
function delete_ido_watchlist($watchlist_id, $user_id)
{
    global $link;
    $user_id = intval($user_id);
    $watchlist_id = intval($watchlist_id);
    $delete_query = "DELETE FROM ido_watchlists WHERE id = $watchlist_id AND user_id = $user_id";
    $delete_result = mysqli_query($link, $delete_query);
    if ($delete_result) {
        if (mysqli_affected_rows($link) > 0) {
            $response = new SuccessResult("IDO watchlist successfully deleted.");
            $response->send();
        } else {
            $response = new ErrorResult("No IDO watchlist found for the given ID and user.");
            $response->send(404);
        }
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to delete IDO watchlist. Error: $error");
        $response->send(500);
    }
}

/**
 * Edit an IDO watchlist name, icon, and description
 *
 * @param int $watchlist_id The ID of the watchlist
 * @param string $watchlist_name The new name of the watchlist
 * @param int $icon_id The ID of the icon
 * @param int $user_id The ID of the user
 * @param string|null $description Optional description of the watchlist
 * @return void
 */
function edit_ido_watchlist($watchlist_id, $watchlist_name, $icon_id, $user_id, $description = null)
{
    global $link;
    $user_id = intval($user_id);
    $watchlist_id = intval($watchlist_id);
    $icon_id = intval($icon_id);
    $watchlist_name = mysqli_real_escape_string($link, $watchlist_name);
    // description NULL kontrolü
    // Eğer description null, undefined veya boş string ise NULL olarak ayarla
    if ($description === null || $description === '' || trim($description) === '') {
        $description = "NULL";
    } else {
        $description = "'" . mysqli_real_escape_string($link, $description) . "'";
    }
    $update_query = "UPDATE ido_watchlists SET name = '$watchlist_name', icon_id = $icon_id, description = $description WHERE id = $watchlist_id AND user_id = $user_id";
    $update_result = mysqli_query($link, $update_query);
    if ($update_result) {
        if (mysqli_affected_rows($link) > 0) {
            $response = new SuccessResult("IDO watchlist successfully updated.");
            $response->send();
        } else {
            $response = new ErrorResult("No changes made or IDO watchlist not found.");
            $response->send(404);
        }
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to update IDO watchlist. Error: $error");
        $response->send(500);
    }
}

/**
 * Get all IDO watchlists for a user
 *
 * @param int $userid The ID of the user
 * @return void
 */
function get_user_ido_watchlists($userid)
{
    global $link;
    $userid = intval($userid);
    $query = "SELECT id, name, description, icon_id FROM ido_watchlists WHERE user_id = $userid";
    $result = mysqli_query($link, $query);
    if ($result) {
        $watchlists = mysqli_fetch_all($result, MYSQLI_ASSOC);
        $all_watchlists = [];
        foreach ($watchlists as &$watchlist) {
            $watchlist_id = $watchlist['id'];
            // Modified query to match fields from get_upcoming_idos_v2
            $ido_query = "
            SELECT
                cp.id,
                cp.cr_name,
                cp.symbol,
                cp.image,
                COALESCE(cp.total_score, FLOOR(RAND() * 100)) as total_score,
                COALESCE(cp.imc_score, FLOOR(RAND() * 100)) as imc_score,
                COALESCE(cp.funding_score, FLOOR(RAND() * 100)) as funding_score,
                COALESCE(cp.launchpad_score, FLOOR(RAND() * 100)) as launchpad_score,
                COALESCE(cp.investor_score, FLOOR(RAND() * 100)) as investor_score,
                COALESCE(cp.social_score, FLOOR(RAND() * 100)) as social_score,
                cp.crowdsale_startdate,
                cp.crowdsale_enddate,
                cp.ido_type
            FROM ido_watchlist_projects iwp
            JOIN client_ico_coin_list cp ON iwp.ido_id = cp.id
            WHERE iwp.watchlist_id = $watchlist_id
            ";
            $rs = mysqli_query($link, $ido_query);
            $idos = [];
            if ($rs) {
                while ($row = mysqli_fetch_assoc($rs)) {
                    // Use actual launch date from database (Unix timestamp format) - same logic as get_upcoming_idos_v2
                    $launchDate = null;
                    if (!empty($row['crowdsale_startdate']) && $row['crowdsale_startdate'] > 0) {
                        $launchDate = date('Y-m-d', $row['crowdsale_startdate']);
                    } elseif (!empty($row['crowdsale_enddate']) && $row['crowdsale_enddate'] > 0) {
                        $launchDate = date('Y-m-d', $row['crowdsale_enddate']);
                    }

                    // Use actual IDO type from database or default to TBA - same logic as get_upcoming_idos_v2
                    $launchType = !empty($row['ido_type']) ? $row['ido_type'] : "TBA";
                    // Format scores with their status
                    $imcScore = [
                        'score' => (int)$row['imc_score'],
                        'status' => getScoreStatus((int)$row['imc_score'])
                    ];
                    $financingScore = [
                        'score' => (int)$row['funding_score'],
                        'status' => getScoreStatus((int)$row['funding_score'])
                    ];
                    $launchpadScore = [
                        'score' => (int)$row['launchpad_score'],
                        'status' => getScoreStatus((int)$row['launchpad_score'])
                    ];
                    $investorScore = [
                        'score' => (int)$row['investor_score'],
                        'status' => getScoreStatus((int)$row['investor_score'])
                    ];
                    $socialScore = [
                        'score' => (int)$row['social_score'],
                        'status' => getScoreStatus((int)$row['social_score'])
                    ];
                    $totalAiScore = [
                        'score' => (int)$row['total_score'],
                        'status' => getScoreStatus((int)$row['total_score'])
                    ];
                    $idos[] = [
                        'id' => (string)$row['id'],
                        'name' => $row['cr_name'],
                        'symbol' => $row['symbol'],
                        'image' => $row['image'],
                        'launchDate' => $launchDate,
                        'launchType' => $launchType,
                        'imcScore' => $imcScore,
                        'financingScore' => $financingScore,
                        'launchpadScore' => $launchpadScore,
                        'investorScore' => $investorScore,
                        'totalAiScore' => $totalAiScore,
                        'socialScore' => $socialScore
                    ];
                }
            }
            $watchlist['projects'] = $idos;
            $all_watchlists[] = $watchlist;
        }
        $response = new SuccessResult($all_watchlists);
        $response->send();
    } else {
        $response = new ErrorResult('Failed to retrieve IDO watchlists.');
        $response->send(500);
    }
}
