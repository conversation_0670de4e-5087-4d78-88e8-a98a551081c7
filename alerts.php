<?php
require_once 'utils.php';
require_once 'config.php';
require_once 'models/ResultModel.php';

// Websocket için gere<PERSON> k<PERSON>üphaneler
// Not: <PERSON>u kütüphanelerin composer il<PERSON> <PERSON><PERSON><PERSON><PERSON>ere<PERSON>
// composer require ratchet/pawl
// composer require react/event-loop

/**
 * Creates a new alert for a user
 *
 * @param int $user_id The ID of the user
 * @param string $coin_id The ID of the coin
 * @param string $alert_type The type of alert (ai-score, price)
 * @param string $notification_type The type of notification (browser, email, push, websocket)
 * @param bool $condition_above Whether to trigger when value goes above threshold
 * @param bool $condition_below Whether to trigger when value goes below threshold
 * @param string|null $threshold_above The threshold value for above condition (for ai-score)
 * @param string|null $threshold_below The threshold value for below condition (for ai-score)
 * @param string|null $price_above The price threshold for above condition (for price)
 * @param string|null $price_below The price threshold for below condition (for price)
 * @return void
 */
function create_alert(
    $user_id,
    $coin_id,
    $alert_type,
    $notification_type,
    $condition_above = false,
    $condition_below = false,
    $threshold_above = null,
    $threshold_below = null,
    $price_above = null,
    $price_below = null
) {
    global $link;

    // İzin kontrolü - Kullanıcının alert ekleyip ekleyemeyeceğini kontrol et
    $hasAlertPermission = hasUserPermission($user_id, 'can_add_alerts');

    // Debug için log ekle
    require_once 'stripe/StripeLogger.php';
    StripeLogger::log(StripeLogLevel::DEBUG, "Alert permission check", [
        'user_id' => $user_id,
        'has_alert_permission' => $hasAlertPermission
    ]);

    if (!$hasAlertPermission) {
        $response = new ErrorResult("You don't have permission to create alerts. Please upgrade your subscription.");
        $response->send(403);
        return;
    }

    // Kullanıcının daha fazla alert ekleyip ekleyemeyeceğini kontrol et
    $canAddMore = canUserAddMoreAlerts($user_id);

    // Debug için log ekle
    StripeLogger::log(StripeLogLevel::DEBUG, "Can add more alerts check", [
        'user_id' => $user_id,
        'can_add_more' => $canAddMore
    ]);

    if (!$canAddMore) {
        global $link;

        // Kullanıcının abonelik seviyesini güvenli şekilde al (Stripe subscription status ile doğrula)
        $query = "SELECT u.subscription_level, s.status as stripe_status, s.plan_name
                  FROM users u
                  LEFT JOIN stripe_user_subscriptions s ON u.id = s.user_id
                      AND s.status IN ('active', 'trialing')
                  WHERE u.id = ?
                  ORDER BY s.created_at DESC
                  LIMIT 1";
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        $userSubscriptionLevel = $user['subscription_level'] ?? 'free';
        $stripeStatus = $user['stripe_status'];

        // Security check: Only allow paid features if Stripe subscription is active
        $subscriptionLevel = ($userSubscriptionLevel !== 'free' && !empty($stripeStatus)) ? $userSubscriptionLevel : 'free';

        // Security logging if there's a mismatch
        if ($userSubscriptionLevel !== 'free' && empty($stripeStatus)) {
            StripeLogger::log(StripeLogLevel::WARNING, "SECURITY: Alert limit check - paid subscription level but no active Stripe subscription", [
                'user_id' => $user_id,
                'user_subscription_level' => $userSubscriptionLevel,
                'stripe_status' => $stripeStatus,
                'final_subscription_level' => $subscriptionLevel
            ]);
        }

        // Debug için log ekle
        StripeLogger::log(StripeLogLevel::DEBUG, "User subscription level in alert limit check", [
            'user_id' => $user_id,
            'subscription_level' => $subscriptionLevel,
            'validated_with_stripe' => !empty($stripeStatus)
        ]);

        // Kullanıcının mevcut alert sayısını al
        $query = "SELECT COUNT(*) as count FROM user_alerts WHERE user_id = ? AND is_active = 1";
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        $currentCount = $row['count'];

        // Abonelik seviyesine göre limit belirle
        $limit = 0;
        $nextLevel = '';

        if ($subscriptionLevel === 'free') {
            $limit = 0;
            $nextLevel = 'Basic';
        } else if ($subscriptionLevel === 'basic') {
            $limit = 5;
            $nextLevel = 'Advance';
        } else if ($subscriptionLevel === 'advance') {
            $limit = 20;
            $nextLevel = 'Premium';
        } else if ($subscriptionLevel === 'premium') {
            $limit = 100; // High limit for premium users
            $nextLevel = '';
        }

        $errorMessage = "You have reached your alert limit ($currentCount/$limit). Please upgrade to $nextLevel for more alerts.";

        $response = new ErrorResult($errorMessage);
        $response->send(403);
        return;
    }

    // Validate inputs
    $user_id = intval($user_id);
    $coin_id = mysqli_real_escape_string($link, $coin_id);
    $alert_type = mysqli_real_escape_string($link, $alert_type);
    $notification_type = mysqli_real_escape_string($link, $notification_type);
    $condition_above = $condition_above ? 1 : 0;
    $condition_below = $condition_below ? 1 : 0;

    // Validate alert type
    if (!in_array($alert_type, ['ai-score', 'price'])) {
        $response = new ErrorResult("Invalid alert type. Supported types: ai-score, price");
        $response->send(400);
        return;
    }

    // Validate notification type
    if (!in_array($notification_type, ['browser', 'email', 'push', 'websocket'])) {
        $response = new ErrorResult("Invalid notification type. Supported types: browser, email, push, websocket");
        $response->send(400);
        return;
    }

    // Validate conditions
    if (!$condition_above && !$condition_below) {
        $response = new ErrorResult("At least one condition (above or below) must be set");
        $response->send(400);
        return;
    }

    // Prepare threshold values based on alert type
    if ($alert_type === 'ai-score') {
        // For AI score alerts
        $threshold_above = $condition_above ? floatval($threshold_above) : "NULL";
        $threshold_below = $condition_below ? floatval($threshold_below) : "NULL";
        $price_above = "NULL";
        $price_below = "NULL";

        // Validate AI score thresholds (0-100)
        if ($condition_above && ($threshold_above < 0 || $threshold_above > 100)) {
            $response = new ErrorResult("AI score threshold must be between 0 and 100");
            $response->send(400);
            return;
        }

        if ($condition_below && ($threshold_below < 0 || $threshold_below > 100)) {
            $response = new ErrorResult("AI score threshold must be between 0 and 100");
            $response->send(400);
            return;
        }
    } else if ($alert_type === 'price') {
        // For price alerts
        $threshold_above = "NULL";
        $threshold_below = "NULL";
        $price_above = $condition_above ? floatval($price_above) : "NULL";
        $price_below = $condition_below ? floatval($price_below) : "NULL";

        // Validate price thresholds (must be positive)
        if ($condition_above && $price_above <= 0) {
            $response = new ErrorResult("Price threshold must be greater than 0");
            $response->send(400);
            return;
        }

        if ($condition_below && $price_below <= 0) {
            $response = new ErrorResult("Price threshold must be greater than 0");
            $response->send(400);
            return;
        }
    }

    // Insert the alert into the database
    $query = "INSERT INTO user_alerts (
        user_id,
        coin_id,
        alert_type,
        notification_type,
        condition_above,
        condition_below,
        threshold_above,
        threshold_below,
        price_above,
        price_below
    ) VALUES (
        $user_id,
        '$coin_id',
        '$alert_type',
        '$notification_type',
        $condition_above,
        $condition_below,
        $threshold_above,
        $threshold_below,
        $price_above,
        $price_below
    )";

    $result = mysqli_query($link, $query);

    if ($result) {
        $response = new SuccessResult([
            'message' => 'Alert created successfully'
        ]);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to create alert: $error");
        $response->send(500);
    }
}

/**
 * Get all alerts for a user
 *
 * @param int $user_id The ID of the user
 * @return void
 */
function get_user_alerts($user_id)
{
    global $link;

    // Validate input
    $user_id = intval($user_id);

    // Query to get all active alerts for the user
    $query = "
        SELECT
            a.id,
            a.coin_id,
            c.name AS coin_name,
            c.symbol AS coin_symbol,
            c.image AS coin_image,
            a.alert_type,
            a.notification_type,
            a.condition_above,
            a.condition_below,
            a.threshold_above,
            a.threshold_below,
            a.price_above,
            a.price_below,
            a.created_at,
            a.is_triggered_above,
            a.is_triggered_below,
            a.triggered_above_at,
            a.triggered_below_at
        FROM
            user_alerts a
        LEFT JOIN
            coindata c ON a.coin_id = c.id
        WHERE
            a.user_id = $user_id AND
            a.is_active = 1
        ORDER BY
            a.created_at DESC
    ";

    $result = mysqli_query($link, $query);

    if ($result) {
        $alerts = [];

        while ($row = mysqli_fetch_assoc($result)) {
            // Format the alert data
            $alert = [
                'id' => $row['id'],
                'coinId' => $row['coin_id'],
                'coinName' => $row['coin_name'] ?? 'Unknown',
                'coinSymbol' => $row['coin_symbol'] ?? 'UNKNOWN',
                'coinImage' => $row['coin_image'] ?? '',
                'type' => $row['alert_type'],
                'notificationType' => $row['notification_type'],
                'conditions' => [],
                'status' => 'active'
            ];

            // Add conditions based on alert type
            if ($row['alert_type'] === 'ai-score') {
                if ($row['condition_above']) {
                    $alert['conditions'][] = [
                        'type' => 'above',
                        'value' => floatval($row['threshold_above']),
                        'isTriggered' => (bool)$row['is_triggered_above'],
                        'triggeredAt' => $row['triggered_above_at']
                    ];
                }

                if ($row['condition_below']) {
                    $alert['conditions'][] = [
                        'type' => 'below',
                        'value' => floatval($row['threshold_below']),
                        'isTriggered' => (bool)$row['is_triggered_below'],
                        'triggeredAt' => $row['triggered_below_at']
                    ];
                }
            } else if ($row['alert_type'] === 'price') {
                if ($row['condition_above']) {
                    $alert['conditions'][] = [
                        'type' => 'above',
                        'value' => floatval($row['price_above']),
                        'isTriggered' => (bool)$row['is_triggered_above'],
                        'triggeredAt' => $row['triggered_above_at']
                    ];
                }

                if ($row['condition_below']) {
                    $alert['conditions'][] = [
                        'type' => 'below',
                        'value' => floatval($row['price_below']),
                        'isTriggered' => (bool)$row['is_triggered_below'],
                        'triggeredAt' => $row['triggered_below_at']
                    ];
                }
            }

            $alert['createdAt'] = $row['created_at'];

            // Determine overall status
            $hasTriggeredCondition = false;
            foreach ($alert['conditions'] as $condition) {
                if ($condition['isTriggered']) {
                    $hasTriggeredCondition = true;
                    break;
                }
            }

            if ($hasTriggeredCondition) {
                $alert['status'] = 'partially_triggered';
            }

            // Check if all conditions are triggered
            $allTriggered = true;
            foreach ($alert['conditions'] as $condition) {
                if (!$condition['isTriggered']) {
                    $allTriggered = false;
                    break;
                }
            }

            if ($allTriggered && count($alert['conditions']) > 0) {
                $alert['status'] = 'fully_triggered';
            }

            $alerts[] = $alert;
        }

        $response = new SuccessResult($alerts);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to retrieve alerts: $error");
        $response->send(500);
    }
}

/**
 * Get all notifications for a user
 *
 * @param int $user_id The ID of the user
 * @return void
 */
function get_user_notifications_from_db($user_id)
{
    global $link;

    // Validate input
    $user_id = intval($user_id);

    // Query to get all notifications for the user
    $query = "
        SELECT
            id,
            title,
            message,
            type,
            priority,
            coin_id,
            link,
            data,
            created_at,
            is_read,
            read_at
        FROM
            user_notifications
        WHERE
            user_id = $user_id
        ORDER BY
            created_at DESC
        LIMIT 50
    ";

    $result = mysqli_query($link, $query);

    if ($result) {
        $notifications = [];

        while ($row = mysqli_fetch_assoc($result)) {
            $notification = [
                'id' => $row['id'],
                'title' => $row['title'],
                'message' => $row['message'],
                'type' => $row['type'],
                'priority' => $row['priority'],
                'status' => $row['is_read'] ? 'read' : 'unread',
                'timestamp' => $row['created_at'],
                'coinId' => $row['coin_id']
            ];

            if ($row['link']) {
                $notification['link'] = $row['link'];
            }

            if ($row['data']) {
                $data = json_decode($row['data'], true);
                if ($data) {
                    $notification['data'] = $data;
                }
            }

            $notifications[] = $notification;
        }

        $response = new SuccessResult($notifications);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to retrieve notifications: $error");
        $response->send(500);
    }
}

/**
 * Mark notification as read
 *
 * @param int $user_id The ID of the user
 * @param int $notification_id The ID of the notification
 * @return void
 */
function mark_notification_read($user_id, $notification_id)
{
    global $link;

    // Validate inputs
    $user_id = intval($user_id);
    $notification_id = intval($notification_id);

    // Check if notification belongs to user
    $check_query = "SELECT id FROM user_notifications WHERE id = $notification_id AND user_id = $user_id";
    $check_result = mysqli_query($link, $check_query);

    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        $response = new ErrorResult("Notification not found or does not belong to the user");
        $response->send(404);
        return;
    }

    // Mark as read
    $update_query = "UPDATE user_notifications SET is_read = 1, read_at = NOW() WHERE id = $notification_id AND user_id = $user_id";
    $update_result = mysqli_query($link, $update_query);

    if ($update_result) {
        $response = new SuccessResult(['message' => 'Notification marked as read']);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to mark notification as read: $error");
        $response->send(500);
    }
}

/**
 * Get unread notifications count for a user
 *
 * @param int $user_id The ID of the user
 * @return void
 */
function get_unread_notifications_count($user_id)
{
    global $link;

    // Validate input
    $user_id = intval($user_id);

    $query = "SELECT COUNT(*) as count FROM user_notifications WHERE user_id = $user_id AND is_read = 0";
    $result = mysqli_query($link, $query);

    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $response = new SuccessResult(['count' => intval($row['count'])]);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to get unread count: $error");
        $response->send(500);
    }
}

/**
 * Get recent notifications for a user (for polling)
 *
 * @param int $user_id The ID of the user
 * @param string $since Timestamp to get notifications since
 * @return void
 */
function get_notifications_since($user_id, $since = null)
{
    global $link;

    // Validate input
    $user_id = intval($user_id);

    $since_condition = "";
    if ($since) {
        $since = mysqli_real_escape_string($link, $since);
        $since_condition = "AND created_at > '$since'";
    }

    $query = "
        SELECT
            id,
            title,
            message,
            type,
            priority,
            coin_id,
            link,
            data,
            created_at,
            is_read,
            read_at
        FROM
            user_notifications
        WHERE
            user_id = $user_id
            $since_condition
        ORDER BY
            created_at DESC
        LIMIT 20
    ";

    $result = mysqli_query($link, $query);

    if ($result) {
        $notifications = [];

        while ($row = mysqli_fetch_assoc($result)) {
            $notification = [
                'id' => $row['id'],
                'title' => $row['title'],
                'message' => $row['message'],
                'type' => $row['type'],
                'priority' => $row['priority'],
                'status' => $row['is_read'] ? 'read' : 'unread',
                'timestamp' => $row['created_at'],
                'coinId' => $row['coin_id']
            ];

            if ($row['link']) {
                $notification['link'] = $row['link'];
            }

            if ($row['data']) {
                $data = json_decode($row['data'], true);
                if ($data) {
                    $notification['data'] = $data;
                }
            }

            $notifications[] = $notification;
        }

        $response = new SuccessResult([
            'notifications' => $notifications,
            'count' => count($notifications),
            'hasNew' => count($notifications) > 0
        ]);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to retrieve notifications: $error");
        $response->send(500);
    }
}

/**
 * Reset triggered conditions for an alert
 *
 * @param int $user_id The ID of the user
 * @param int $alert_id The ID of the alert to reset
 * @param string $condition_type The condition to reset ('above', 'below', or 'all')
 * @return void
 */
function reset_alert_triggers($user_id, $alert_id, $condition_type = 'all')
{
    global $link;

    // Validate inputs
    $user_id = intval($user_id);
    $alert_id = intval($alert_id);
    $condition_type = mysqli_real_escape_string($link, $condition_type);

    // Check if the alert exists and belongs to the user
    $check_query = "SELECT id FROM user_alerts WHERE id = $alert_id AND user_id = $user_id";
    $check_result = mysqli_query($link, $check_query);

    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        $response = new ErrorResult("Alert not found or does not belong to the user");
        $response->send(404);
        return;
    }

    // Build update query based on condition type
    $update_fields = [];
    if ($condition_type === 'above' || $condition_type === 'all') {
        $update_fields[] = "is_triggered_above = 0, triggered_above_at = NULL";
    }
    if ($condition_type === 'below' || $condition_type === 'all') {
        $update_fields[] = "is_triggered_below = 0, triggered_below_at = NULL";
    }

    if (empty($update_fields)) {
        $response = new ErrorResult("Invalid condition type. Use 'above', 'below', or 'all'");
        $response->send(400);
        return;
    }

    $update_query = "UPDATE user_alerts SET " . implode(', ', $update_fields) . " WHERE id = $alert_id AND user_id = $user_id";
    $update_result = mysqli_query($link, $update_query);

    if ($update_result) {
        $response = new SuccessResult([
            'message' => 'Alert triggers reset successfully'
        ]);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to reset alert triggers: $error");
        $response->send(500);
    }
}

/**
 * Delete an alert
 *
 * @param int $user_id The ID of the user
 * @param int $alert_id The ID of the alert to delete
 * @return void
 */
function delete_alert($user_id, $alert_id)
{
    global $link;

    // Validate inputs
    $user_id = intval($user_id);
    $alert_id = intval($alert_id);

    // Check if the alert exists and belongs to the user
    $check_query = "SELECT id FROM user_alerts WHERE id = $alert_id AND user_id = $user_id";
    $check_result = mysqli_query($link, $check_query);

    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        $response = new ErrorResult("Alert not found or does not belong to the user");
        $response->send(404);
        return;
    }

    // Delete the alert (or mark it as inactive)
    $delete_query = "UPDATE user_alerts SET is_active = 0 WHERE id = $alert_id AND user_id = $user_id";
    $delete_result = mysqli_query($link, $delete_query);

    if ($delete_result) {
        $response = new SuccessResult([
            'message' => 'Alert deleted successfully'
        ]);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to delete alert: $error");
        $response->send(500);
    }
}
