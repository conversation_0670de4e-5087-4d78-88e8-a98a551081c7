/**
 * WebSocket Token Refresh Integration Example
 * Frontend'de otomatik token yenileme için gerekli kodlar
 */

// 1. Token refresh fonksiyonu (API'den yeni token almak için)
async function refreshAuthToken() {
    try {
        const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getCurrentToken()}` // Mevcut token
            },
            credentials: 'include' // Refresh token cookie için
        });

        if (!response.ok) {
            throw new Error(`Token refresh failed: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.success && data.data && data.data.auth_token) {
            // Yeni token'ı localStorage'a kaydet
            localStorage.setItem('auth_token', data.data.auth_token);
            return data.data.auth_token;
        } else {
            throw new Error('Invalid token refresh response');
        }
    } catch (error) {
        console.error('❌ Token refresh error:', error);
        throw error;
    }
}

// 2. Mevcut token'ı alma fonksiyonu
function getCurrentToken() {
    return localStorage.getItem('auth_token') || '';
}

// 3. WebSocket Client'ı token refresh ile başlatma
function initializeWebSocketWithTokenRefresh() {
    const authToken = getCurrentToken();
    
    if (!authToken) {
        console.error('❌ No auth token found');
        return null;
    }

    // WebSocket client'ı oluştur
    const wsClient = new CoinScoutWebSocketClient(
        'ws://localhost:8080',
        authToken,
        { 
            debug: true,
            disconnectOnTokenRefreshFailure: true // Token refresh başarısız olursa bağlantıyı kes
        }
    );

    // Token refresh'i başlat (4 dakikada bir)
    wsClient.startTokenRefresh(refreshAuthToken, 4 * 60 * 1000);

    // Event listener'ları ekle
    wsClient.on('connected', () => {
        console.log('✅ WebSocket connected');
    });

    wsClient.on('authenticated', () => {
        console.log('🔐 WebSocket authenticated');
    });

    wsClient.on('token_refreshed', (data) => {
        console.log('🔄 Token refreshed successfully');
        // İsteğe bağlı: UI'da token refresh indicator'ı göster
        showTokenRefreshIndicator();
    });

    wsClient.on('token_refresh_failed', (error) => {
        console.error('❌ Token refresh failed:', error);
        // Kullanıcıyı login sayfasına yönlendir
        handleTokenRefreshFailure();
    });

    wsClient.on('disconnected', (data) => {
        console.log('🔌 WebSocket disconnected:', data);
        // Token refresh'i durdur
        wsClient.stopTokenRefresh();
    });

    return wsClient;
}

// 4. Token refresh başarısızlığını handle etme
function handleTokenRefreshFailure() {
    // Token'ı temizle
    localStorage.removeItem('auth_token');
    
    // Kullanıcıya bildirim göster
    alert('Oturum süreniz doldu. Lütfen tekrar giriş yapın.');
    
    // Login sayfasına yönlendir
    window.location.href = '/login';
}

// 5. Token refresh indicator (isteğe bağlı)
function showTokenRefreshIndicator() {
    const indicator = document.getElementById('token-refresh-indicator');
    if (indicator) {
        indicator.style.display = 'block';
        indicator.textContent = '🔄 Token yenilendi';
        
        // 3 saniye sonra gizle
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 3000);
    }
}

// 6. React Hook örneği
function useWebSocketWithTokenRefresh() {
    const [wsClient, setWsClient] = useState(null);
    const [isConnected, setIsConnected] = useState(false);
    const [tokenRefreshCount, setTokenRefreshCount] = useState(0);

    useEffect(() => {
        const client = initializeWebSocketWithTokenRefresh();
        
        if (client) {
            client.on('connected', () => setIsConnected(true));
            client.on('disconnected', () => setIsConnected(false));
            client.on('token_refreshed', () => {
                setTokenRefreshCount(prev => prev + 1);
            });
            
            setWsClient(client);
        }

        // Cleanup
        return () => {
            if (client) {
                client.disconnect();
            }
        };
    }, []);

    return { wsClient, isConnected, tokenRefreshCount };
}

// 7. Manuel token refresh (isteğe bağlı)
function manualTokenRefresh(wsClient) {
    if (wsClient && wsClient.tokenRefreshCallback) {
        wsClient.tokenRefreshCallback()
            .then(newToken => {
                wsClient.updateAuthToken(newToken);
                console.log('✅ Manual token refresh successful');
            })
            .catch(error => {
                console.error('❌ Manual token refresh failed:', error);
            });
    }
}

// 8. Token süresini kontrol etme (isteğe bağlı)
function getTokenExpirationTime(token) {
    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.exp * 1000; // milliseconds
    } catch (error) {
        return null;
    }
}

function isTokenExpiringSoon(token, thresholdMinutes = 1) {
    const expirationTime = getTokenExpirationTime(token);
    if (!expirationTime) return false;
    
    const now = Date.now();
    const threshold = thresholdMinutes * 60 * 1000;
    
    return (expirationTime - now) < threshold;
}

// 9. Kullanım örneği
document.addEventListener('DOMContentLoaded', () => {
    // WebSocket'i token refresh ile başlat
    const wsClient = initializeWebSocketWithTokenRefresh();
    
    if (wsClient) {
        // Notification event'lerini ekle
        wsClient.on('notification', (notification) => {
            console.log('🔔 New notification:', notification);
            showNotification(notification);
        });

        wsClient.on('notification_read_confirmed', (data) => {
            console.log('✅ Notification marked as read:', data.notificationId);
            markNotificationAsReadInUI(data.notificationId);
        });

        // Global olarak erişilebilir yap
        window.wsClient = wsClient;
    }
});

// 10. HTML'de kullanım
/*
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Token Refresh Example</title>
</head>
<body>
    <!-- Token refresh indicator -->
    <div id="token-refresh-indicator" style="display: none; position: fixed; top: 10px; right: 10px; background: #28a745; color: white; padding: 10px; border-radius: 4px;"></div>
    
    <!-- Manuel refresh butonu -->
    <button onclick="manualTokenRefresh(window.wsClient)">Manuel Token Refresh</button>
    
    <script src="websocket/client.js"></script>
    <script src="websocket/token-refresh-example.js"></script>
</body>
</html>
*/
