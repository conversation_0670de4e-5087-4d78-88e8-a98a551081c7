/**
 * React Component Example for CoinScout WebSocket Integration
 * This shows how to integrate the WebSocket client with React
 */

import React, { useState, useEffect, useRef } from 'react';

// Import the WebSocket client (adjust path as needed)
// import CoinScoutWebSocketClient from './client.js';

const NotificationSystem = ({ authToken, userId }) => {
    const [isConnected, setIsConnected] = useState(false);
    const [notifications, setNotifications] = useState([]);
    const [unreadCount, setUnreadCount] = useState(0);
    const [connectionStatus, setConnectionStatus] = useState('disconnected');
    const wsClient = useRef(null);

    useEffect(() => {
        if (authToken) {
            initializeWebSocket();
        }

        return () => {
            if (wsClient.current) {
                wsClient.current.disconnect();
            }
        };
    }, [authToken]);

    const initializeWebSocket = () => {
        // Initialize WebSocket client
        wsClient.current = new CoinScoutWebSocketClient(
            'ws://localhost:8080', // Adjust URL as needed
            authToken,
            {
                debug: true,
                reconnectInterval: 5000,
                maxReconnectAttempts: 10
            }
        );

        // Connection events
        wsClient.current.on('connected', () => {
            setConnectionStatus('connected');
            setIsConnected(true);
            console.log('🔗 WebSocket connected');
        });

        wsClient.current.on('disconnected', (data) => {
            setConnectionStatus('disconnected');
            setIsConnected(false);
            console.log('🔌 WebSocket disconnected:', data);
        });

        wsClient.current.on('authenticated', (data) => {
            setConnectionStatus('authenticated');
            console.log('🔐 WebSocket authenticated:', data);
        });

        // Notification events
        wsClient.current.on('notification', (notification) => {
            console.log('🔔 New notification:', notification);

            // Add to notifications list
            setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50

            // Update unread count
            setUnreadCount(prev => prev + 1);

            // Show browser notification if permission granted
            showBrowserNotification(notification);

            // Play notification sound (optional)
            playNotificationSound();
        });

        wsClient.current.on('unread_notifications', (data) => {
            console.log('📬 Unread notifications:', data);
            setNotifications(data.notifications || []);
            setUnreadCount(data.count || 0);
        });

        wsClient.current.on('notification_read_confirmed', (data) => {
            console.log('✅ Notification read confirmed:', data);
            // Update local state to mark notification as read
            setNotifications(prev =>
                prev.map(notif =>
                    notif.id === data.notificationId
                        ? { ...notif, status: 'read', isRead: true }
                        : notif
                )
            );
        });

        wsClient.current.on('unread_count_update', (data) => {
            console.log('📊 Unread count updated:', data);
            setUnreadCount(data.count);
        });

        wsClient.current.on('error', (error) => {
            console.error('❌ WebSocket error:', error);
            setConnectionStatus('error');
        });

        wsClient.current.on('max_reconnect_attempts_reached', () => {
            console.error('❌ Max reconnect attempts reached');
            setConnectionStatus('failed');
        });
    };

    const showBrowserNotification = (notification) => {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.message,
                icon: '/favicon.ico', // Adjust path as needed
                tag: `coinscout-${notification.id}`,
                requireInteraction: false
            });
        }
    };

    const playNotificationSound = () => {
        // Optional: Play notification sound
        try {
            const audio = new Audio('/notification-sound.mp3'); // Add your sound file
            audio.volume = 0.3;
            audio.play().catch(e => console.log('Could not play notification sound:', e));
        } catch (error) {
            console.log('Notification sound not available:', error);
        }
    };

    const requestNotificationPermission = async () => {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            console.log('Notification permission:', permission);
        }
    };

    const markNotificationAsRead = (notificationId) => {
        try {
            // Use WebSocket to mark notification as read
            if (wsClient.current && isConnected) {
                const success = wsClient.current.markNotificationAsRead(notificationId);
                if (!success) {
                    console.error('Failed to send mark as read request via WebSocket');
                    // Fallback to API call if WebSocket fails
                    markNotificationAsReadViaAPI(notificationId);
                }
            } else {
                console.warn('WebSocket not connected, using API fallback');
                // Fallback to API call if WebSocket is not connected
                markNotificationAsReadViaAPI(notificationId);
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
            // Fallback to API call on error
            markNotificationAsReadViaAPI(notificationId);
        }
    };

    // Fallback API method
    const markNotificationAsReadViaAPI = async (notificationId) => {
        try {
            const response = await fetch('/api/notifications/mark-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({ notificationId })
            });

            if (response.ok) {
                // Update local state
                setNotifications(prev =>
                    prev.map(notif =>
                        notif.id === notificationId
                            ? { ...notif, status: 'read', isRead: true }
                            : notif
                    )
                );

                // Decrease unread count
                setUnreadCount(prev => Math.max(0, prev - 1));
            }
        } catch (error) {
            console.error('Error marking notification as read via API:', error);
        }
    };

    const clearAllNotifications = () => {
        setNotifications([]);
        setUnreadCount(0);
    };

    const getConnectionStatusColor = () => {
        switch (connectionStatus) {
            case 'connected':
            case 'authenticated':
                return 'green';
            case 'disconnected':
                return 'orange';
            case 'error':
            case 'failed':
                return 'red';
            default:
                return 'gray';
        }
    };

    return (
        <div className="notification-system">
            {/* Connection Status */}
            <div className="connection-status">
                <span
                    className={`status-indicator ${connectionStatus}`}
                    style={{
                        display: 'inline-block',
                        width: '10px',
                        height: '10px',
                        borderRadius: '50%',
                        backgroundColor: getConnectionStatusColor(),
                        marginRight: '8px'
                    }}
                />
                WebSocket: {connectionStatus}
            </div>

            {/* Notification Bell */}
            <div className="notification-bell" style={{ position: 'relative', display: 'inline-block' }}>
                <button
                    onClick={() => setShowNotifications(!showNotifications)}
                    style={{
                        background: 'none',
                        border: 'none',
                        fontSize: '24px',
                        cursor: 'pointer',
                        position: 'relative'
                    }}
                >
                    🔔
                    {unreadCount > 0 && (
                        <span
                            style={{
                                position: 'absolute',
                                top: '-5px',
                                right: '-5px',
                                background: 'red',
                                color: 'white',
                                borderRadius: '50%',
                                width: '20px',
                                height: '20px',
                                fontSize: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                        >
                            {unreadCount > 99 ? '99+' : unreadCount}
                        </span>
                    )}
                </button>
            </div>

            {/* Notification Panel */}
            {showNotifications && (
                <div
                    className="notification-panel"
                    style={{
                        position: 'absolute',
                        top: '100%',
                        right: '0',
                        width: '400px',
                        maxHeight: '500px',
                        backgroundColor: 'white',
                        border: '1px solid #ccc',
                        borderRadius: '8px',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        zIndex: 1000,
                        overflow: 'hidden'
                    }}
                >
                    <div
                        className="notification-header"
                        style={{
                            padding: '16px',
                            borderBottom: '1px solid #eee',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }}
                    >
                        <h3 style={{ margin: 0 }}>Notifications ({unreadCount})</h3>
                        <div>
                            <button onClick={requestNotificationPermission} style={{ marginRight: '8px' }}>
                                🔔 Enable
                            </button>
                            <button onClick={clearAllNotifications}>
                                🗑️ Clear
                            </button>
                        </div>
                    </div>

                    <div
                        className="notification-list"
                        style={{
                            maxHeight: '400px',
                            overflowY: 'auto'
                        }}
                    >
                        {notifications.length === 0 ? (
                            <div style={{ padding: '32px', textAlign: 'center', color: '#666' }}>
                                No notifications
                            </div>
                        ) : (
                            notifications.map((notification) => (
                                <div
                                    key={notification.id}
                                    className={`notification-item ${notification.status === 'read' ? 'read' : 'unread'}`}
                                    style={{
                                        padding: '16px',
                                        borderBottom: '1px solid #eee',
                                        backgroundColor: notification.status === 'read' ? '#f9f9f9' : 'white',
                                        cursor: 'pointer'
                                    }}
                                    onClick={() => markNotificationAsRead(notification.id)}
                                >
                                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                                        {notification.title}
                                    </div>
                                    <div style={{ color: '#666', fontSize: '14px', marginBottom: '8px' }}>
                                        {notification.message}
                                    </div>
                                    <div style={{ fontSize: '12px', color: '#999' }}>
                                        {new Date(notification.timestamp).toLocaleString()}
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default NotificationSystem;
