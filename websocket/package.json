{"name": "coinscout-websocket-server", "version": "1.0.0", "description": "WebSocket server for CoinScout real-time notifications", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-client.js"}, "dependencies": {"ws": "^8.14.2", "mysql2": "^3.6.5", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "axios": "^1.6.2", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["websocket", "notifications", "real-time", "coinscout"], "author": "CoinScout Team", "license": "MIT"}