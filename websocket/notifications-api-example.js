/**
 * Notifications API Integration Example
 * Professional frontend integration for notification management
 */

class NotificationsAPI {
    constructor(baseUrl = '/client.php', authToken = null) {
        this.baseUrl = baseUrl;
        this.authToken = authToken;
    }

    // Set or update auth token
    setAuthToken(token) {
        this.authToken = token;
    }

    // Make authenticated API request
    async makeRequest(endpoint, data = {}) {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: JSON.stringify({
                    f: endpoint,
                    ...data
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || 'API request failed');
            }

            return result.data;
        } catch (error) {
            console.error(`❌ API Error (${endpoint}):`, error);
            throw error;
        }
    }

    /**
     * Get all notifications (read and unread)
     * Returns last 50 notifications ordered by newest first
     */
    async getAllNotifications() {
        return await this.makeRequest('get_notifications');
    }

    /**
     * Get unread notifications count
     */
    async getUnreadCount() {
        const result = await this.makeRequest('get_unread_notifications_count');
        return result.count;
    }

    /**
     * Get notifications since a specific timestamp
     * @param {string} since - ISO timestamp or MySQL datetime format
     */
    async getNotificationsSince(since) {
        return await this.makeRequest('get_notifications_since', { since });
    }

    /**
     * Mark a notification as read
     * @param {number} notificationId - ID of the notification
     */
    async markAsRead(notificationId) {
        return await this.makeRequest('mark_notification_read', { 
            notification_id: notificationId 
        });
    }

    /**
     * Mark multiple notifications as read
     * @param {number[]} notificationIds - Array of notification IDs
     */
    async markMultipleAsRead(notificationIds) {
        const promises = notificationIds.map(id => this.markAsRead(id));
        return await Promise.all(promises);
    }
}

// React Hook for notifications
function useNotifications() {
    const [notifications, setNotifications] = useState([]);
    const [unreadCount, setUnreadCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const api = useMemo(() => {
        const token = localStorage.getItem('auth_token');
        return new NotificationsAPI('/client.php', token);
    }, []);

    // Load all notifications
    const loadNotifications = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const data = await api.getAllNotifications();
            setNotifications(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [api]);

    // Load unread count
    const loadUnreadCount = useCallback(async () => {
        try {
            const count = await api.getUnreadCount();
            setUnreadCount(count);
        } catch (err) {
            console.error('Failed to load unread count:', err);
        }
    }, [api]);

    // Mark notification as read
    const markAsRead = useCallback(async (notificationId) => {
        try {
            await api.markAsRead(notificationId);
            
            // Update local state
            setNotifications(prev => 
                prev.map(notif => 
                    notif.id === notificationId 
                        ? { ...notif, status: 'read' }
                        : notif
                )
            );
            
            // Update unread count
            setUnreadCount(prev => Math.max(0, prev - 1));
            
            return true;
        } catch (err) {
            console.error('Failed to mark notification as read:', err);
            return false;
        }
    }, [api]);

    // Mark all as read
    const markAllAsRead = useCallback(async () => {
        const unreadNotifications = notifications.filter(n => n.status === 'unread');
        const unreadIds = unreadNotifications.map(n => n.id);
        
        if (unreadIds.length === 0) return true;

        try {
            await api.markMultipleAsRead(unreadIds);
            
            // Update local state
            setNotifications(prev => 
                prev.map(notif => ({ ...notif, status: 'read' }))
            );
            setUnreadCount(0);
            
            return true;
        } catch (err) {
            console.error('Failed to mark all as read:', err);
            return false;
        }
    }, [api, notifications]);

    // Initial load
    useEffect(() => {
        loadNotifications();
        loadUnreadCount();
    }, [loadNotifications, loadUnreadCount]);

    return {
        notifications,
        unreadCount,
        loading,
        error,
        loadNotifications,
        loadUnreadCount,
        markAsRead,
        markAllAsRead
    };
}

// Vanilla JavaScript usage example
class NotificationManager {
    constructor() {
        this.api = new NotificationsAPI();
        this.notifications = [];
        this.unreadCount = 0;
        this.listeners = [];
    }

    // Initialize with auth token
    init(authToken) {
        this.api.setAuthToken(authToken);
        this.loadNotifications();
        this.loadUnreadCount();
    }

    // Add event listener
    on(event, callback) {
        this.listeners.push({ event, callback });
    }

    // Emit event
    emit(event, data) {
        this.listeners
            .filter(l => l.event === event)
            .forEach(l => l.callback(data));
    }

    // Load all notifications
    async loadNotifications() {
        try {
            this.notifications = await this.api.getAllNotifications();
            this.emit('notifications_loaded', this.notifications);
        } catch (error) {
            this.emit('error', error);
        }
    }

    // Load unread count
    async loadUnreadCount() {
        try {
            this.unreadCount = await this.api.getUnreadCount();
            this.emit('unread_count_updated', this.unreadCount);
        } catch (error) {
            this.emit('error', error);
        }
    }

    // Mark as read
    async markAsRead(notificationId) {
        try {
            await this.api.markAsRead(notificationId);
            
            // Update local data
            const notification = this.notifications.find(n => n.id === notificationId);
            if (notification && notification.status === 'unread') {
                notification.status = 'read';
                this.unreadCount = Math.max(0, this.unreadCount - 1);
                
                this.emit('notification_read', notificationId);
                this.emit('unread_count_updated', this.unreadCount);
            }
        } catch (error) {
            this.emit('error', error);
        }
    }

    // Get notifications by status
    getNotificationsByStatus(status) {
        return this.notifications.filter(n => n.status === status);
    }

    // Get unread notifications
    getUnreadNotifications() {
        return this.getNotificationsByStatus('unread');
    }

    // Get read notifications
    getReadNotifications() {
        return this.getNotificationsByStatus('read');
    }
}

// Usage examples
const examples = {
    // Basic API usage
    basicUsage: async () => {
        const api = new NotificationsAPI('/client.php', 'your-jwt-token');
        
        // Get all notifications
        const notifications = await api.getAllNotifications();
        console.log('All notifications:', notifications);
        
        // Get unread count
        const unreadCount = await api.getUnreadCount();
        console.log('Unread count:', unreadCount);
        
        // Mark notification as read
        await api.markAsRead(20);
        console.log('Notification 20 marked as read');
    },

    // Notification Manager usage
    managerUsage: () => {
        const manager = new NotificationManager();
        
        // Set up event listeners
        manager.on('notifications_loaded', (notifications) => {
            console.log('Notifications loaded:', notifications);
            updateNotificationsList(notifications);
        });
        
        manager.on('unread_count_updated', (count) => {
            console.log('Unread count:', count);
            updateUnreadBadge(count);
        });
        
        manager.on('notification_read', (notificationId) => {
            console.log('Notification read:', notificationId);
            markNotificationAsReadInUI(notificationId);
        });
        
        // Initialize
        const token = localStorage.getItem('auth_token');
        manager.init(token);
    }
};

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { NotificationsAPI, NotificationManager };
}

// Global usage
if (typeof window !== 'undefined') {
    window.NotificationsAPI = NotificationsAPI;
    window.NotificationManager = NotificationManager;
    window.useNotifications = useNotifications;
}
