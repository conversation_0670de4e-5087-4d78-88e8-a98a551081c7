<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CoinScout WebSocket Notification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        
        .notification {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .notification.unread {
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .notification.read {
            opacity: 0.7;
            border-left: 4px solid #6c757d;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { 
            background-color: #6c757d; 
            cursor: not-allowed; 
        }
        
        .unread-count {
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 8px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CoinScout WebSocket Notification Test</h1>
        
        <div>
            <label for="authToken">Auth Token:</label>
            <input type="text" id="authToken" placeholder="JWT token buraya girin">
            <button onclick="connect()">Bağlan</button>
            <button onclick="disconnect()">Bağlantıyı Kes</button>
        </div>
        
        <div id="status" class="status disconnected">Bağlantı Yok</div>
        
        <h3>Bildirimler <span id="unreadCount" class="unread-count" style="display: none;">0</span></h3>
        <div id="notifications"></div>
        
        <h3>Test İşlemleri</h3>
        <button onclick="requestUnreadNotifications()" id="requestBtn" disabled>Okunmamış Bildirimleri Getir</button>
        
        <h3>Log</h3>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">Log'u Temizle</button>
    </div>

    <script src="client.js"></script>
    <script>
        let wsClient = null;
        let notifications = [];

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = message;
        }

        function updateUnreadCount(count) {
            const countSpan = document.getElementById('unreadCount');
            if (count > 0) {
                countSpan.textContent = count;
                countSpan.style.display = 'inline';
            } else {
                countSpan.style.display = 'none';
            }
        }

        function renderNotifications() {
            const container = document.getElementById('notifications');
            container.innerHTML = '';
            
            notifications.forEach(notification => {
                const div = document.createElement('div');
                div.className = `notification ${notification.isRead ? 'read' : 'unread'}`;
                div.innerHTML = `
                    <div><strong>${notification.title}</strong></div>
                    <div>${notification.message}</div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        ID: ${notification.id} | ${new Date(notification.timestamp).toLocaleString()}
                    </div>
                    ${!notification.isRead ? 
                        `<button onclick="markAsRead(${notification.id})">Okundu İşaretle</button>` : 
                        '<span style="color: #28a745;">✓ Okundu</span>'
                    }
                `;
                container.appendChild(div);
            });
        }

        function connect() {
            const token = document.getElementById('authToken').value;
            if (!token) {
                alert('Lütfen auth token girin');
                return;
            }

            updateStatus('connecting', 'Bağlanıyor...');
            
            wsClient = new CoinScoutWebSocketClient('ws://localhost:8080', token, { debug: true });

            wsClient.on('connected', () => {
                updateStatus('connected', 'Bağlandı');
                document.getElementById('requestBtn').disabled = false;
                log('✅ WebSocket bağlantısı kuruldu');
            });

            wsClient.on('authenticated', () => {
                log('🔐 Kimlik doğrulama başarılı');
            });

            wsClient.on('disconnected', () => {
                updateStatus('disconnected', 'Bağlantı Kesildi');
                document.getElementById('requestBtn').disabled = true;
                log('❌ WebSocket bağlantısı kesildi');
            });

            wsClient.on('notification', (notification) => {
                log(`🔔 Yeni bildirim: ${notification.title}`);
                notifications.unshift({...notification, isRead: false});
                renderNotifications();
                updateUnreadCount(notifications.filter(n => !n.isRead).length);
            });

            wsClient.on('unread_notifications', (data) => {
                log(`📬 ${data.count} okunmamış bildirim alındı`);
                notifications = data.notifications.map(n => ({...n, isRead: false}));
                renderNotifications();
                updateUnreadCount(data.count);
            });

            wsClient.on('notification_read_confirmed', (data) => {
                log(`✅ Bildirim ${data.notificationId} okundu olarak işaretlendi`);
                notifications = notifications.map(n => 
                    n.id === data.notificationId ? {...n, isRead: true} : n
                );
                renderNotifications();
            });

            wsClient.on('unread_count_update', (data) => {
                log(`📊 Okunmamış bildirim sayısı güncellendi: ${data.count}`);
                updateUnreadCount(data.count);
            });

            wsClient.on('error', (error) => {
                log(`❌ Hata: ${error}`);
            });
        }

        function disconnect() {
            if (wsClient) {
                wsClient.disconnect();
                wsClient = null;
            }
        }

        function requestUnreadNotifications() {
            if (wsClient && wsClient.isConnected) {
                log('📬 Okunmamış bildirimler isteniyor...');
                // Server otomatik olarak bağlantı kurulduğunda okunmamış bildirimleri gönderir
                // Ama manuel olarak da istenebilir
            }
        }

        function markAsRead(notificationId) {
            if (wsClient && wsClient.isConnected) {
                log(`📖 Bildirim ${notificationId} okundu olarak işaretleniyor...`);
                wsClient.markNotificationAsRead(notificationId);
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
    </script>
</body>
</html>
