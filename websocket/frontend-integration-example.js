/**
 * CoinScout WebSocket Notification Integration Example
 * Frontend'de notification read functionality için gerekli kodlar
 */

// 1. WebSocket Client'ı başlat
const wsClient = new CoinScoutWebSocketClient(
    'ws://localhost:8080', // WebSocket server URL'i
    authToken, // JWT auth token
    { debug: true }
);

// 2. Event listener'ları ekle
wsClient.on('connected', () => {
    console.log('✅ WebSocket bağlandı');
});

wsClient.on('authenticated', () => {
    console.log('🔐 Kimlik doğrulama başarılı');
});

// Yeni bildirim geldiğinde
wsClient.on('notification', (notification) => {
    console.log('🔔 Yeni bildirim:', notification);
    
    // Notification'ı UI'a ekle
    addNotificationToUI(notification);
    
    // Unread counter'ı artır
    incrementUnreadCounter();
});

// Okunmamış bildirimler geldiğinde (sayfa yüklendiğinde)
wsClient.on('unread_notifications', (data) => {
    console.log('📬 Okunmamış bildirimler:', data);
    
    // Tüm notification'ları UI'a ekle
    displayNotifications(data.notifications);
    
    // Unread counter'ı güncelle
    updateUnreadCounter(data.count);
});

// Notification okundu onayı geldiğinde
wsClient.on('notification_read_confirmed', (data) => {
    console.log('✅ Notification okundu:', data.notificationId);
    
    // UI'da notification'ı okundu olarak işaretle
    markNotificationAsReadInUI(data.notificationId);
});

// Okunmamış bildirim sayısı güncellendiğinde
wsClient.on('unread_count_update', (data) => {
    console.log('📊 Unread count güncellendi:', data.count);
    
    // Unread counter'ı güncelle
    updateUnreadCounter(data.count);
});

// 3. Notification'ı okundu olarak işaretle
function markAsRead(notificationId) {
    if (wsClient && wsClient.isConnected) {
        console.log(`📖 Marking notification ${notificationId} as read...`);
        wsClient.markNotificationAsRead(notificationId);
    } else {
        console.warn('⚠️ WebSocket bağlı değil, API fallback kullanılıyor');
        // Fallback: API call
        markAsReadViaAPI(notificationId);
    }
}

// 4. UI Helper Functions (örnek implementasyonlar)

function addNotificationToUI(notification) {
    // Notification'ı UI'a ekle
    const notificationElement = createNotificationElement(notification);
    document.getElementById('notifications-container').prepend(notificationElement);
}

function displayNotifications(notifications) {
    // Tüm notification'ları göster
    const container = document.getElementById('notifications-container');
    container.innerHTML = '';
    
    notifications.forEach(notification => {
        const element = createNotificationElement(notification);
        container.appendChild(element);
    });
}

function createNotificationElement(notification) {
    const div = document.createElement('div');
    div.className = `notification ${notification.isRead ? 'read' : 'unread'}`;
    div.setAttribute('data-notification-id', notification.id);
    
    div.innerHTML = `
        <div class="notification-header">
            <h4>${notification.title}</h4>
            <span class="notification-time">${formatTime(notification.timestamp)}</span>
        </div>
        <div class="notification-message">${notification.message}</div>
        <div class="notification-actions">
            ${!notification.isRead ? 
                `<button onclick="markAsRead(${notification.id})" class="mark-read-btn">
                    Okundu İşaretle
                </button>` : 
                '<span class="read-indicator">✓ Okundu</span>'
            }
        </div>
    `;
    
    return div;
}

function markNotificationAsReadInUI(notificationId) {
    // UI'da notification'ı okundu olarak işaretle
    const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
    if (element) {
        element.classList.remove('unread');
        element.classList.add('read');
        
        // Button'ı "Okundu" indicator'ı ile değiştir
        const actionsDiv = element.querySelector('.notification-actions');
        actionsDiv.innerHTML = '<span class="read-indicator">✓ Okundu</span>';
    }
}

function updateUnreadCounter(count) {
    // Unread counter'ı güncelle
    const counter = document.getElementById('unread-counter');
    if (counter) {
        counter.textContent = count;
        counter.style.display = count > 0 ? 'inline' : 'none';
    }
    
    // Badge'i güncelle
    const badge = document.getElementById('notification-badge');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline' : 'none';
    }
}

function incrementUnreadCounter() {
    const counter = document.getElementById('unread-counter');
    if (counter) {
        const currentCount = parseInt(counter.textContent) || 0;
        updateUnreadCounter(currentCount + 1);
    }
}

// 5. API Fallback (WebSocket bağlı değilse)
async function markAsReadViaAPI(notificationId) {
    try {
        const response = await fetch('/api/notifications/mark-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({ notificationId })
        });

        if (response.ok) {
            console.log('✅ Notification API ile okundu işaretlendi');
            markNotificationAsReadInUI(notificationId);
            
            // Unread count'u manuel olarak azalt
            const counter = document.getElementById('unread-counter');
            if (counter) {
                const currentCount = parseInt(counter.textContent) || 0;
                updateUnreadCounter(Math.max(0, currentCount - 1));
            }
        }
    } catch (error) {
        console.error('❌ API ile notification işaretleme hatası:', error);
    }
}

// 6. Utility Functions
function formatTime(timestamp) {
    return new Date(timestamp).toLocaleString('tr-TR');
}

// 7. CSS Styles (örnek)
const styles = `
.notification {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    background: white;
    transition: all 0.3s ease;
}

.notification.unread {
    border-left: 4px solid #007bff;
    background-color: #f8f9fa;
}

.notification.read {
    opacity: 0.7;
    border-left: 4px solid #6c757d;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.notification-time {
    font-size: 12px;
    color: #666;
}

.mark-read-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.read-indicator {
    color: #28a745;
    font-size: 12px;
    font-weight: bold;
}

#unread-counter, #notification-badge {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 8px;
    font-size: 12px;
    margin-left: 5px;
}
`;

// CSS'i sayfaya ekle
const styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);
