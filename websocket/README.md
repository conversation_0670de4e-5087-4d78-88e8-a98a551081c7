# CoinScout WebSocket Notification System

Bu Node.js WebSocket sunucusu, CoinScout uygulaması için gerçek zamanlı bildirim sistemi sağlar. User_alerts tablosundaki koşulları kontrol eder ve tetiklenen alertler için user_notifications tablosuna kayıt ekleyerek WebSocket üzerinden frontend'e anında bildirim gönderir.

## 🚀 Özellikler

- **Gerçek Zamanlı Bildirimler**: WebSocket ile anında bildirim
- **Alert Sistemi**: Fiyat ve AI skor alertlerini otomatik kontrol
- **JWT Kimlik Doğrulama**: Güvenli kullanıcı doğrulama
- **Otomatik Yeniden Bağlanma**: Bağlantı koptuğunda otomatik yeniden bağlanma
- **Çoklu Bağlantı**: Bir kullanıcının birden fazla cihazdan bağlanabilmesi
- **Heartbeat**: Bağlantı durumunu kontrol etme

## 📋 Gereksinimler

- Node.js 16+
- MySQL veritabanı
- CoinGecko API erişimi (fiyat verileri için)

## 🛠️ Kurulum

### 1. Bağımlılıkları Yükle

```bash
cd websocket
npm install
```

### 2. Ortam Değişkenlerini Ayarla

`.env.example` dosyasını `.env` olarak kopyalayın ve değerleri düzenleyin:

```bash
cp .env.example .env
```

`.env` dosyasını düzenleyin:

```env
# Database Configuration
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name
DB_PORT=3306

# WebSocket Server Configuration
WS_PORT=8080
WS_HOST=0.0.0.0

# JWT Configuration
JWT_SECRET=48Scw74aAgf16wvAhhr85411A18w3

# CoinGecko API
COINGECKO_API_URL=https://api.coingecko.com/api/v3

# Alert Check Interval (dakika)
ALERT_CHECK_INTERVAL=5

# Logging
LOG_LEVEL=info
```

### 3. Veritabanı Tablolarını Kontrol Et

Gerekli tablolar mevcut olmalı:
- `user_alerts` - Alert tanımları
- `user_notifications` - Bildirim kayıtları
- `coindata` - Coin verileri
- `users` - Kullanıcı bilgileri

## 🚀 Çalıştırma

### Geliştirme Modu
```bash
npm run dev
```

### Üretim Modu
```bash
npm start
```

### Test Client
```bash
npm test
```

## 📡 WebSocket API

### Bağlantı
```javascript
const ws = new WebSocket('ws://localhost:8080');
```

### Kimlik Doğrulama
```javascript
ws.send(JSON.stringify({
    type: 'auth',
    token: 'YOUR_JWT_TOKEN'
}));
```

### Mesaj Tipleri

#### Gelen Mesajlar (Server → Client)

**Kimlik Doğrulama Başarılı**
```json
{
    "type": "auth_success",
    "message": "Authentication successful",
    "userId": 123
}
```

**Yeni Bildirim**
```json
{
    "type": "new_notification",
    "notification": {
        "id": 456,
        "title": "Bitcoin Alert Triggered",
        "message": "Bitcoin price above $50000: $51000",
        "type": "price",
        "priority": "high",
        "coinId": "bitcoin",
        "timestamp": "2024-01-01T12:00:00Z"
    }
}
```

**Okunmamış Bildirimler**
```json
{
    "type": "unread_notifications",
    "notifications": [...],
    "count": 5
}
```

**Bildirim Okundu Onayı**
```json
{
    "type": "notification_read_confirmed",
    "notificationId": 123,
    "success": true
}
```

**Okunmamış Bildirim Sayısı Güncelleme**
```json
{
    "type": "unread_count_update",
    "count": 3
}
```

#### Giden Mesajlar (Client → Server)

**Ping (Heartbeat)**
```json
{
    "type": "ping"
}
```

**Bildirimi Okundu Olarak İşaretle**
```json
{
    "type": "mark_notification_read",
    "notificationId": 123
}
```

## 🔧 Frontend Entegrasyonu

### JavaScript Client Kullanımı

```javascript
// Client'ı başlat
const wsClient = new CoinScoutWebSocketClient(
    'ws://localhost:8080',
    authToken,
    { debug: true }
);

// Event listener'ları ekle
wsClient.on('notification', (notification) => {
    console.log('Yeni bildirim:', notification);
    showNotificationToUser(notification);
});

wsClient.on('unread_notifications', (data) => {
    updateNotificationCounter(data.count);
});

wsClient.on('notification_read_confirmed', (data) => {
    console.log('Bildirim okundu:', data.notificationId);
    markNotificationAsReadInUI(data.notificationId);
});

wsClient.on('unread_count_update', (data) => {
    updateNotificationCounter(data.count);
});

// Bildirimi okundu olarak işaretle
function markNotificationAsRead(notificationId) {
    wsClient.markNotificationAsRead(notificationId);
}
```

### React Entegrasyonu

`react-example.jsx` dosyasında tam React entegrasyonu örneği bulunmaktadır.

## 🔍 Alert Sistemi

### Desteklenen Alert Tipleri

1. **Fiyat Alertleri** (`price`)
   - Fiyat belirli değerin üstüne çıktığında
   - Fiyat belirli değerin altına düştüğünde

2. **AI Skor Alertleri** (`ai-score`)
   - AI skoru belirli değerin üstüne çıktığında
   - AI skoru belirli değerin altına düştüğünde

### Alert Kontrol Süreci

1. Her 5 dakikada bir (yapılandırılabilir) aktif alertler kontrol edilir
2. Fiyat alertleri için CoinGecko API'den güncel fiyatlar alınır
3. AI skor alertleri için veritabanından güncel skorlar alınır
4. Koşullar sağlandığında:
   - Alert `is_triggered = 1` olarak işaretlenir
   - `user_notifications` tablosuna kayıt eklenir
   - WebSocket üzerinden kullanıcıya bildirim gönderilir

## 🐛 Hata Ayıklama

### Debug Modu
```bash
LOG_LEVEL=debug npm start
```

### Test Client ile Test
```bash
npm test
```

### Loglar
- Bağlantı durumu
- Kimlik doğrulama
- Alert kontrolleri
- Bildirim gönderimi
- Hata mesajları

## 🔒 Güvenlik

- JWT token ile kimlik doğrulama
- Kullanıcı izolasyonu (her kullanıcı sadece kendi bildirimlerini alır)
- Rate limiting (gelecek sürümlerde)
- CORS koruması (gelecek sürümlerde)

## 📈 Performans

- Veritabanı bağlantı havuzu
- Efficient alert sorguları
- WebSocket bağlantı yönetimi
- Memory leak koruması

## 🚀 Deployment

### PM2 ile Çalıştırma
```bash
npm install -g pm2
pm2 start server.js --name "coinscout-websocket"
pm2 save
pm2 startup
```

### Docker (Opsiyonel)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 8080
CMD ["npm", "start"]
```

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun
3. Değişikliklerinizi commit edin
4. Pull request gönderin

## 📄 Lisans

MIT License
