<?php
require_once 'utils.php';
require_once 'mailsender.php';
require_once('models/ResultModel.php');
require_once('stripe/StripeLogger.php');
/**
 * Generate a verification token for email verification
 *
 * @param int $length Length of the token
 * @return string The generated token
 */
function generate_verification_token($length = 32)
{
    StripeLogger::log(StripeLogLevel::DEBUG, "[EMAIL_VERIFICATION] Generating verification token", ['length' => $length]);

    $token = bin2hex(random_bytes($length));

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Verification token generated successfully", ['token_length' => strlen($token)]);

    return $token;
}
/**
 * Send verification email to the user
 *
 * @param string $email User's email address
 * @param string $token Verification token
 * @param string $username User's username
 * @return bool Whether the email was sent successfully
 */
function send_verification_email($email, $token, $username)
{
    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Starting verification email send process", [
        'email' => $email,
        'username' => $username,
        'token_length' => strlen($token)
    ]);

    // Generate verification link
    $verification_link = "https://coinscout.app/verify-email?token=" . $token;

    StripeLogger::log(StripeLogLevel::DEBUG, "[EMAIL_VERIFICATION] Verification link generated", [
        'verification_link' => $verification_link
    ]);
    // Email template
    $message_body = "
<!DOCTYPE html>
<html>
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>CoinScout Email Verification</title>
    <style>
        /* Base styles */
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .email-header {
            background-color: #172130; /* Dark background matching app */
            color: #ffffff;
            padding: 24px 30px;
            text-align: center;
        }
        .logo {
            margin-bottom: 15px;
        }
        .email-body {
            padding: 30px;
            color: #334155;
        }
        .email-footer {
            background-color: #f8fafc;
            padding: 20px 30px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
            border-top: 1px solid #e2e8f0;
        }
        h1 {
            color: #1AD5FF; /* Primary color */
            margin-top: 0;
            margin-bottom: 20px;
            font-weight: 600;
            font-size: 24px;
        }
        p {
            margin-bottom: 16px;
        }
        .btn {
            display: inline-block;
            background-color: #1AD5FF; /* Primary app color */
            color: #172130 !important; /* Dark color for contrast on button */
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0fc3eb; /* Slightly darker on hover */
        }
        .notice {
            background-color: rgba(26, 213, 255, 0.1); /* Light primary color bg */
            border-left: 4px solid #1AD5FF;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .signature {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class=\"email-container\">
        <div class=\"email-header\">
            <div class=\"logo\">
                <!-- CoinScout Logo -->
                <img src=\"https://coinscout.app/assets/Primary-BgPv7JH9.png\" alt=\"CoinScout\" style=\"max-width: 180px; height: auto;\">
            </div>
            <h1 style=\"color: #ffffff; margin: 0;\">Email Verification</h1>
        </div>
        <div class=\"email-body\">
            <p>Hello $username,</p>
            <p>Thank you for registering with CoinScout, your advanced cryptocurrency investment platform.</p>
            <p>To ensure the security of your account and access all features, please verify your email address by clicking the button below:</p>
            <div style=\"text-align: center;\">
                <a href=\"$verification_link\" class=\"btn\">Verify Email Address</a>
            </div>
            <div class=\"notice\">
                <p style=\"margin: 0;\"><strong>Note:</strong> This verification link will expire in 24 hours for security reasons.</p>
            </div>
            <p>If you did not create an account with CoinScout, please ignore this email.</p>
            <div class=\"signature\">
                <p style=\"margin-bottom: 5px;\">Best regards,</p>
                <p style=\"margin-bottom: 0; font-weight: 600;\">The CoinScout Team</p>
            </div>
        </div>
        <div class=\"email-footer\">
            <p>&copy; 2025 CoinScout. All rights reserved.</p>
            <p>Advanced cryptocurrency investment platform</p>
        </div>
    </div>
</body>
</html>
    ";
    // Send email
    try {
        StripeLogger::log(StripeLogLevel::DEBUG, "[EMAIL_VERIFICATION] Attempting to send verification email", [
            'email' => $email,
            'subject' => 'CoinScout Email Verification'
        ]);

        send_mail($message_body, [$email], "CoinScout Email Verification");

        StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Verification email sent successfully", [
            'email' => $email,
            'username' => $username
        ]);

        return true;
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to send verification email", [
            'email' => $email,
            'username' => $username,
            'error' => $e->getMessage(),
            'error_code' => $e->getCode()
        ]);

        error_log("Failed to send verification email to $email: " . $e->getMessage());
        return false;
    }
}
/**
 * Store verification token in the database
 *
 * @param int $userId User ID
 * @param string $token Verification token
 * @return bool Whether the token was stored successfully
 */
function store_verification_token($userId, $token)
{
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Storing verification token in database", [
        'user_id' => $userId,
        'token_length' => strlen($token)
    ]);

    $query = "UPDATE users SET
              email_verification_token = ?,
              email_verification_sent_at = NOW()
              WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    if (!$stmt) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to prepare database statement", [
            'user_id' => $userId,
            'error' => mysqli_error($link)
        ]);

        error_log("Failed to prepare statement: " . mysqli_error($link));
        return false;
    }
    mysqli_stmt_bind_param($stmt, "si", $token, $userId);
    if (!mysqli_stmt_execute($stmt)) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to execute database statement", [
            'user_id' => $userId,
            'error' => mysqli_error($link)
        ]);

        error_log("Failed to store verification token: " . mysqli_error($link));
        return false;
    }

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Verification token stored successfully", [
        'user_id' => $userId
    ]);

    return true;
}
/**
 * Verify email with token
 *
 * @param string $token Verification token
 * @return array Result of verification
 */
function verify_email($token)
{
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Starting email verification process", [
        'token_length' => strlen($token ?? '')
    ]);

    if (empty($token)) {
        StripeLogger::log(StripeLogLevel::WARNING, "[EMAIL_VERIFICATION] Empty verification token provided");

        return [
            'success' => false,
            'message' => 'Invalid verification token'
        ];
    }

    // Check if token exists and is valid (not expired)
    $query = "SELECT id FROM users
              WHERE email_verification_token = ?
              AND email_verified = 0
              AND email_verification_sent_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    $stmt = mysqli_prepare($link, $query);
    if (!$stmt) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to prepare token verification query", [
            'error' => mysqli_error($link)
        ]);

        return [
            'success' => false,
            'message' => 'Database error'
        ];
    }

    mysqli_stmt_bind_param($stmt, "s", $token);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (!$user = mysqli_fetch_assoc($result)) {
        StripeLogger::log(StripeLogLevel::WARNING, "[EMAIL_VERIFICATION] Invalid or expired verification token", [
            'token_length' => strlen($token)
        ]);

        return [
            'success' => false,
            'message' => 'Invalid or expired verification token'
        ];
    }

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Valid token found, marking email as verified", [
        'user_id' => $user['id']
    ]);

    // Mark email as verified
    $updateQuery = "UPDATE users SET
                   email_verified = 1,
                   email_verification_token = NULL
                   WHERE id = ?";
    $updateStmt = mysqli_prepare($link, $updateQuery);
    if (!$updateStmt) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to prepare email verification update query", [
            'user_id' => $user['id'],
            'error' => mysqli_error($link)
        ]);

        return [
            'success' => false,
            'message' => 'Database error'
        ];
    }

    mysqli_stmt_bind_param($updateStmt, "i", $user['id']);
    if (!mysqli_stmt_execute($updateStmt)) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to execute email verification update", [
            'user_id' => $user['id'],
            'error' => mysqli_error($link)
        ]);

        return [
            'success' => false,
            'message' => 'Failed to verify email'
        ];
    }

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Email verified successfully", [
        'user_id' => $user['id']
    ]);

    return [
        'success' => true,
        'message' => 'Email verified successfully'
    ];
}
function resend_verification_email($userId)
{
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Starting resend verification email process", [
        'user_id' => $userId
    ]);

    // Get user information
    $query = "SELECT email, username, email_verified FROM users WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "i", $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user = mysqli_fetch_assoc($result);

    if (!$user) {
        StripeLogger::log(StripeLogLevel::WARNING, "[EMAIL_VERIFICATION] User not found for resend verification", [
            'user_id' => $userId
        ]);

        $response = new ErrorResult('User not found');
        $response->send(404);
        return;
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "[EMAIL_VERIFICATION] User found for resend verification", [
        'user_id' => $userId,
        'email' => $user['email'],
        'username' => $user['username'],
        'email_verified' => $user['email_verified']
    ]);

    // Check if email is already verified
    if ($user['email_verified'] == 1) {
        StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Email already verified, no resend needed", [
            'user_id' => $userId,
            'email' => $user['email']
        ]);

        $response = new SuccessResult('Email is already verified');
        $response->send();
        return;
    }

    // Generate new verification token
    $verification_token = generate_verification_token();

    // Store new token
    if (!store_verification_token($userId, $verification_token)) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to store new verification token for resend", [
            'user_id' => $userId,
            'email' => $user['email']
        ]);

        $response = new ErrorResult('Failed to generate verification token');
        $response->send(500);
        return;
    }

    // Send verification email
    if (!send_verification_email($user['email'], $verification_token, $user['username'])) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to resend verification email", [
            'user_id' => $userId,
            'email' => $user['email'],
            'username' => $user['username']
        ]);

        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['verification_email_failed']);
        $response->send(500);
        return;
    }

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Verification email resent successfully", [
        'user_id' => $userId,
        'email' => $user['email'],
        'username' => $user['username']
    ]);

    global $selectedLanguage, $errorMessages;
    $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $response = new SuccessResult($errorMessages[$lang]['verification_email_sent'] ?? 'Verification email sent successfully');
    $response->send();
    return;
}

/**
 * Resend verification email by email address (for non-authenticated users)
 *
 * @param string $email User's email address
 * @return void
 */
function resend_verification_email_by_email($email)
{
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Starting resend verification email by email process", [
        'email' => $email
    ]);

    if (empty($email)) {
        StripeLogger::log(StripeLogLevel::WARNING, "[EMAIL_VERIFICATION] Empty email provided for resend verification");

        $response = new ErrorResult('Email address is required');
        $response->send(400);
        return;
    }

    // Get user information by email
    $query = "SELECT id, email, username, email_verified FROM users WHERE email = ?";
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "s", $email);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user = mysqli_fetch_assoc($result);

    if (!$user) {
        StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Email not found in database for resend verification (security response)", [
            'email' => $email
        ]);

        // For security reasons, don't reveal if email exists or not
        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new SuccessResult($errorMessages[$lang]['verification_email_sent'] ?? 'If an account exists with this email, a verification email will be sent');
        $response->send();
        return;
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "[EMAIL_VERIFICATION] User found by email for resend verification", [
        'user_id' => $user['id'],
        'email' => $user['email'],
        'username' => $user['username'],
        'email_verified' => $user['email_verified']
    ]);

    // Check if email is already verified
    if ($user['email_verified'] == 1) {
        StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Email already verified for resend by email request", [
            'user_id' => $user['id'],
            'email' => $user['email']
        ]);

        $response = new SuccessResult('Email is already verified');
        $response->send();
        return;
    }

    // Generate new verification token
    $verification_token = generate_verification_token();

    // Store new token
    if (!store_verification_token($user['id'], $verification_token)) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to store new verification token for resend by email", [
            'user_id' => $user['id'],
            'email' => $user['email']
        ]);

        $response = new ErrorResult('Failed to generate verification token');
        $response->send(500);
        return;
    }

    // Send verification email
    $username = !empty($user['username']) ? $user['username'] : 'User';
    if (!send_verification_email($user['email'], $verification_token, $username)) {
        StripeLogger::log(StripeLogLevel::ERROR, "[EMAIL_VERIFICATION] Failed to resend verification email by email", [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'username' => $username
        ]);

        global $selectedLanguage, $errorMessages;
        $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

        $response = new ErrorResult($errorMessages[$lang]['verification_email_failed']);
        $response->send(500);
        return;
    }

    StripeLogger::log(StripeLogLevel::INFO, "[EMAIL_VERIFICATION] Verification email resent successfully by email", [
        'user_id' => $user['id'],
        'email' => $user['email'],
        'username' => $username
    ]);

    global $selectedLanguage, $errorMessages;
    $lang = isset($errorMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $response = new SuccessResult($errorMessages[$lang]['verification_email_sent'] ?? 'Verification email sent successfully');
    $response->send();
    return;
}
