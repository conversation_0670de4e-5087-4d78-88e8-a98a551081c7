<?php
/**
 * Stripe Subscription Update Functions
 *
 * This file contains functions related to updating Stripe subscriptions.
 */

// Include required files if not already included
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../secrets.php';
require_once __DIR__ . '/../utils.php';
require_once __DIR__ . '/../models/ResultModel.php';
require_once __DIR__ . '/StripeLogger.php';
require_once __DIR__ . '/email_notifications.php';
require_once __DIR__ . '/language_strings.php';
require_once __DIR__ . '/../language_config.php';

// Set Stripe API key if not already set
if (!isset($stripeSecretKey)) {
    global $stripeSecretKey;
    \Stripe\Stripe::setApiKey($stripeSecretKey);
}

/**
 * Handle subscription update event
 *
 * @param object $subscription The subscription object from Stripe
 * @param object $event The full Stripe event object (optional)
 */
function handleSubscriptionUpdated($subscription, $event = null) {
    global $link;

    try {
        $subscriptionId = $subscription->id;
        $status = $subscription->status;
        $stripeCustomerId = $subscription->customer; // Used for user lookup
        $currentPeriodEnd = date('Y-m-d H:i:s', $subscription->current_period_end);
        $cancelAtPeriodEnd = $subscription->cancel_at_period_end ? 1 : 0;

        // Get current plan information
        // First try to get from items.data[0].plan which is the newer Stripe API structure
        if (isset($subscription->items) && isset($subscription->items->data) && count($subscription->items->data) > 0) {
            $planId = $subscription->items->data[0]->plan->id ?? null;
            $planAmount = $subscription->items->data[0]->plan->amount ?? null;
            $planInterval = $subscription->items->data[0]->plan->interval ?? null;
            $productId = $subscription->items->data[0]->plan->product ?? null;
            $planNickname = $subscription->items->data[0]->plan->nickname ?? null;
        }
        // Fallback to the older subscription.plan structure
        else {
            $planId = isset($subscription->plan) ? $subscription->plan->id : null;
            $planAmount = isset($subscription->plan) ? $subscription->plan->amount : null;
            $planInterval = isset($subscription->plan) ? $subscription->plan->interval : null;
            $productId = isset($subscription->plan) ? $subscription->plan->product : null;
            $planNickname = isset($subscription->plan) ? $subscription->plan->nickname : null;
        }

        // Get plan name - first try nickname, then try to get product name
        $planName = $planNickname;

        // If nickname is not available, try to get product name from database
        if (empty($planName) && $productId) {
            // Try to get product name from database
            $productQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
            $productStmt = mysqli_prepare($link, $productQuery);

            if ($productStmt) {
                mysqli_stmt_bind_param($productStmt, "s", $productId);
                mysqli_stmt_execute($productStmt);
                $productResult = mysqli_stmt_get_result($productStmt);
                $productData = mysqli_fetch_assoc($productResult);

                if ($productData && isset($productData['name'])) {
                    $planName = $productData['name'];
                    StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE PLAN NAME FROM DATABASE IN UPDATE - Product ID: $productId, Name: $planName");
                }
            }

            // If still no name, try to get from Stripe API
            if (empty($planName)) {
                try {
                    $product = \Stripe\Product::retrieve($productId);
                    if ($product && isset($product->name)) {
                        $planName = $product->name;
                        StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE PLAN NAME FROM API IN UPDATE - Product ID: $productId, Name: $planName");
                    }
                } catch (\Exception $e) {
                    StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PRODUCT RETRIEVAL ERROR IN UPDATE: " . $e->getMessage(), [
                        'exception' => get_class($e),
                        'productId' => $productId ?? 'unknown'
                    ]);
                }
            }
        }

        // If still no name, use a default
        if (empty($planName)) {
            $planName = 'Subscription Plan';
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION UPDATE EVENT - ID: $subscriptionId, Status: $status, Plan ID: $planId");

        // 1. First check metadata for user ID
        $userId = null;
        if (isset($subscription->metadata) && isset($subscription->metadata->user_id)) {
            $metadataUserId = $subscription->metadata->user_id;

            // Find user by metadata user_id
            $query = "SELECT id FROM users WHERE id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "i", $metadataUserId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY METADATA IN UPDATE - User ID: $userId");
            }
        }

        // 2. If no user ID in metadata or not found, try to find by stripe_customer_id
        if (!$userId) {
            $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $stripeCustomerId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CUSTOMER ID IN UPDATE - User ID: $userId");
            }
        }

        // 3. If still no user found, try to find by subscription ID
        if (!$userId) {
            $userQuery = "SELECT user_id FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
            $userStmt = mysqli_prepare($link, $userQuery);

            if (!$userStmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($userStmt, "s", $subscriptionId);
            mysqli_stmt_execute($userStmt);
            $result = mysqli_stmt_get_result($userStmt);
            $subscriptionData = mysqli_fetch_assoc($result);

            if ($subscriptionData) {
                $userId = $subscriptionData['user_id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY SUBSCRIPTION ID IN UPDATE - User ID: $userId");
            }
        }

        // Get the current subscription data before updating
        $previousPlanQuery = "SELECT plan_id, plan_name, plan_amount FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
        $previousPlanStmt = mysqli_prepare($link, $previousPlanQuery);

        if (!$previousPlanStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($previousPlanStmt, "s", $subscriptionId);
        mysqli_stmt_execute($previousPlanStmt);
        $previousPlanResult = mysqli_stmt_get_result($previousPlanStmt);
        $previousPlan = mysqli_fetch_assoc($previousPlanResult);

        // Determine if this is an upgrade or plan change
        $isUpgrade = false;
        $isPlanChange = false;
        $previousSubscriptionLevel = null;
        $previousPlanId = null;

        // Check for plan changes using previous_attributes from the event object
        if ($event && isset($event->data) && isset($event->data->previous_attributes)) {
            $previousAttributes = $event->data->previous_attributes;

            // Check for plan ID change in items.data[0].plan.id (newer Stripe API structure)
            if (isset($previousAttributes->items) &&
                isset($previousAttributes->items->data) &&
                count($previousAttributes->items->data) > 0 &&
                isset($previousAttributes->items->data[0]->plan) &&
                isset($previousAttributes->items->data[0]->plan->id)) {

                $previousPlanId = $previousAttributes->items->data[0]->plan->id;

                if ($previousPlanId !== $planId) {
                    $isPlanChange = true;
                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PLAN ID CHANGED FROM WEBHOOK - From: $previousPlanId To: $planId");
                }
            }
            // Check for plan ID change in plan.id (older Stripe API structure)
            elseif (isset($previousAttributes->plan) && isset($previousAttributes->plan->id)) {
                $previousPlanId = $previousAttributes->plan->id;

                if ($previousPlanId !== $planId) {
                    $isPlanChange = true;
                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PLAN ID CHANGED FROM WEBHOOK - From: $previousPlanId To: $planId");
                }
            }
        }

        // If we couldn't detect plan change from webhook, use database comparison
        if (!$isPlanChange && $previousPlan && isset($previousPlan['plan_id']) && $previousPlan['plan_id'] !== $planId) {
            $isPlanChange = true;
            $previousPlanId = $previousPlan['plan_id'];
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PLAN ID CHANGED FROM DATABASE - From: $previousPlanId To: $planId");
        }

        // Determine subscription levels and check for price-based upgrade/downgrade
        if ($previousPlan && isset($previousPlan['plan_name'])) {
            $previousSubscriptionLevel = determineSubscriptionLevel($previousPlan['plan_name']);
            $newSubscriptionLevel = determineSubscriptionLevel($planName);

            // Check if the plan amount has increased (upgrade)
            if ($planAmount > $previousPlan['plan_amount']) {
                $isUpgrade = true;
                StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION UPGRADE DETECTED - From: $previousSubscriptionLevel ($previousPlan[plan_amount]) To: $newSubscriptionLevel ($planAmount)");
            } elseif ($planAmount < $previousPlan['plan_amount']) {
                StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION DOWNGRADE DETECTED - From: $previousSubscriptionLevel ($previousPlan[plan_amount]) To: $newSubscriptionLevel ($planAmount)");
            }
        }

        // Update subscription in database
        $query = "UPDATE stripe_user_subscriptions SET
                status = ?,
                plan_id = COALESCE(?, plan_id),
                plan_name = COALESCE(?, plan_name),
                plan_amount = COALESCE(?, plan_amount),
                `plan_interval` = COALESCE(?, `plan_interval`),
                current_period_end = ?,
                cancel_at_period_end = ?,
                updated_at = NOW()
                WHERE stripe_subscription_id = ?";

        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param(
            $stmt,
            "sssdssis",
            $status,
            $planId,
            $planName,
            $planAmount,
            $planInterval,
            $currentPeriodEnd,
            $cancelAtPeriodEnd,
            $subscriptionId
        );

        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception("Failed to update subscription: " . mysqli_error($link));
        }

        $affectedRows = mysqli_affected_rows($link);

        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION UPDATED IN DB - Subscription ID: $subscriptionId, Status: $status");

            // If subscription status is active and we have plan info and user ID
            if ($status == 'active' && $planName && $userId) {
                $subscriptionLevel = determineSubscriptionLevel($planName);

                // Update user subscription level
                $updateUserQuery = "UPDATE users SET subscription_level = ? WHERE id = ?";
                $updateUserStmt = mysqli_prepare($link, $updateUserQuery);

                if (!$updateUserStmt) {
                    throw new Exception("Database error: " . mysqli_error($link));
                }

                mysqli_stmt_bind_param($updateUserStmt, "si", $subscriptionLevel, $userId);

                if (mysqli_stmt_execute($updateUserStmt)) {
                    // Handle plan changes based on price or plan ID
                    if ($isUpgrade) {
                        StripeLogger::log(StripeLogLevel::INFO, "STRIPE USER SUBSCRIPTION UPGRADED - User ID: $userId, From: $previousSubscriptionLevel, To: $subscriptionLevel");

                        // Log the upgrade event in a separate table if needed
                        logSubscriptionChange($userId, $subscriptionId, 'upgrade', $previousSubscriptionLevel, $subscriptionLevel);

                        // Apply any immediate benefits for upgrades if needed
                        applyUpgradeBenefits($userId, $previousSubscriptionLevel, $subscriptionLevel);
                    } elseif ($planAmount < $previousPlan['plan_amount']) {
                        StripeLogger::log(StripeLogLevel::INFO, "STRIPE USER SUBSCRIPTION DOWNGRADED - User ID: $userId, From: $previousSubscriptionLevel, To: $subscriptionLevel");

                        // Log the downgrade event
                        logSubscriptionChange($userId, $subscriptionId, 'downgrade', $previousSubscriptionLevel, $subscriptionLevel);

                        // Handle downgrade-specific logic if needed
                        handleSubscriptionDowngrade($userId, $previousSubscriptionLevel, $subscriptionLevel);
                    } elseif ($isPlanChange) {
                        // Plan changed but price might be the same
                        if ($previousSubscriptionLevel !== $subscriptionLevel) {
                            StripeLogger::log(StripeLogLevel::INFO, "STRIPE USER SUBSCRIPTION PLAN CHANGED - User ID: $userId, From: $previousSubscriptionLevel, To: $subscriptionLevel");

                            // Determine if this is an upgrade or downgrade based on subscription level
                            $changeType = 'plan_change';

                            // Log the plan change event
                            logSubscriptionChange($userId, $subscriptionId, $changeType, $previousSubscriptionLevel, $subscriptionLevel);

                            // Apply appropriate benefits based on the new plan
                            if ($subscriptionLevel === 'premium' || $subscriptionLevel === 'pro') {
                                applyUpgradeBenefits($userId, $previousSubscriptionLevel, $subscriptionLevel);
                            } else {
                                handleSubscriptionDowngrade($userId, $previousSubscriptionLevel, $subscriptionLevel);
                            }
                        } else {
                            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PLAN ID CHANGED BUT SUBSCRIPTION LEVEL REMAINS THE SAME - User ID: $userId, Level: $subscriptionLevel");
                        }
                    } else {
                        StripeLogger::log(StripeLogLevel::INFO, "STRIPE USER SUBSCRIPTION LEVEL UPDATED - User ID: $userId, New Level: $subscriptionLevel");
                    }
                } else {
                    StripeLogger::log(StripeLogLevel::ERROR, "FAILED TO UPDATE USER SUBSCRIPTION LEVEL: " . mysqli_error($link), [
                        'userId' => $userId ?? 'unknown',
                        'subscriptionId' => $subscriptionId ?? 'unknown',
                        'subscriptionLevel' => $subscriptionLevel ?? 'unknown'
                    ]);
                }
            }
        } else {
            // If subscription doesn't exist in database, create it
            StripeLogger::log(StripeLogLevel::NOTICE, "STRIPE SUBSCRIPTION NOT FOUND IN DB - Creating new subscription record");
            handleSubscriptionCreated($subscription);
        }

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION UPDATE ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'subscriptionId' => $subscriptionId ?? 'unknown',
            'userId' => $userId ?? 'unknown'
        ]);
        throw $e;
    }
}

/**
 * Log subscription change event
 *
 * @param int $userId User ID
 * @param string $subscriptionId Stripe subscription ID
 * @param string $changeType Type of change (upgrade, downgrade, etc.)
 * @param string $fromLevel Previous subscription level
 * @param string $toLevel New subscription level
 */
function logSubscriptionChange($userId, $subscriptionId, $changeType, $fromLevel, $toLevel) {
    global $link;

    try {
        // Check if stripe_subscription_changes table exists
        $tableCheckQuery = "SHOW TABLES LIKE 'stripe_subscription_changes'";
        $tableCheckResult = mysqli_query($link, $tableCheckQuery);

        // If table doesn't exist, create it
        if (mysqli_num_rows($tableCheckResult) == 0) {
            $createTableQuery = "CREATE TABLE IF NOT EXISTS stripe_subscription_changes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                stripe_subscription_id VARCHAR(255) NOT NULL,
                change_type VARCHAR(50) NOT NULL,
                from_level VARCHAR(50) NOT NULL,
                to_level VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX (user_id),
                INDEX (stripe_subscription_id)
            )";

            if (!mysqli_query($link, $createTableQuery)) {
                throw new Exception("Failed to create subscription changes table: " . mysqli_error($link));
            }
        }

        // Insert the change record
        $insertQuery = "INSERT INTO stripe_subscription_changes
                        (user_id, stripe_subscription_id, change_type, from_level, to_level)
                        VALUES (?, ?, ?, ?, ?)";
        $insertStmt = mysqli_prepare($link, $insertQuery);

        if (!$insertStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($insertStmt, "issss", $userId, $subscriptionId, $changeType, $fromLevel, $toLevel);

        if (!mysqli_stmt_execute($insertStmt)) {
            throw new Exception("Failed to log subscription change: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION CHANGE LOGGED - User ID: $userId, Type: $changeType, From: $fromLevel, To: $toLevel");

    } catch (Exception $e) {
        // Just log the error but don't throw it to avoid disrupting the main flow
        StripeLogger::log(StripeLogLevel::ERROR, "FAILED TO LOG SUBSCRIPTION CHANGE: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'subscriptionId' => $subscriptionId ?? 'unknown',
            'changeType' => $changeType ?? 'unknown'
        ]);
    }
}

/**
 * Apply immediate benefits for subscription upgrades
 *
 * @param int $userId User ID
 * @param string $fromLevel Previous subscription level
 * @param string $toLevel New subscription level
 */
function applyUpgradeBenefits($userId, $fromLevel, $toLevel) {
    global $link;

    try {
        // Log the upgrade action
        StripeLogger::log(StripeLogLevel::INFO, "APPLYING UPGRADE BENEFITS - User ID: $userId, From: $fromLevel, To: $toLevel");

        // Example: If upgrading to premium or pro, grant immediate access to features
        if ($toLevel == 'premium' || $toLevel == 'pro') {
            // Update any user-specific settings or permissions that should be applied immediately
            // For example, increase API rate limits, unlock features, etc.

            // This is a placeholder for any immediate actions that should happen on upgrade
            // The actual implementation will depend on your application's requirements

            // Example: Send notification to user about the upgrade
            $notificationQuery = "INSERT INTO user_notifications
                                 (user_id, type, title, message, is_read)
                                 VALUES (?, 'subscription_upgrade', 'Subscription Upgraded',
                                 'Your subscription has been upgraded to " . ucfirst($toLevel) . ". Enjoy your new benefits!', 0)";

            $notificationStmt = mysqli_prepare($link, $notificationQuery);

            if ($notificationStmt) {
                mysqli_stmt_bind_param($notificationStmt, "i", $userId);
                mysqli_stmt_execute($notificationStmt);
                StripeLogger::log(StripeLogLevel::INFO, "UPGRADE NOTIFICATION SENT - User ID: $userId");
            }
        }

    } catch (Exception $e) {
        // Just log the error but don't throw it to avoid disrupting the main flow
        StripeLogger::log(StripeLogLevel::ERROR, "FAILED TO APPLY UPGRADE BENEFITS: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'fromLevel' => $fromLevel ?? 'unknown',
            'toLevel' => $toLevel ?? 'unknown'
        ]);
    }
}

/**
 * Handle subscription downgrade
 *
 * @param int $userId User ID
 * @param string $fromLevel Previous subscription level
 * @param string $toLevel New subscription level
 */
function handleSubscriptionDowngrade($userId, $fromLevel, $toLevel) {
    global $link;

    try {
        // Log the downgrade action
        StripeLogger::log(StripeLogLevel::INFO, "HANDLING SUBSCRIPTION DOWNGRADE - User ID: $userId, From: $fromLevel, To: $toLevel");

        // Example: Send notification to user about the downgrade
        $notificationQuery = "INSERT INTO user_notifications
                             (user_id, type, title, message, is_read)
                             VALUES (?, 'subscription_downgrade', 'Subscription Changed',
                             'Your subscription has been changed to " . ucfirst($toLevel) . ".', 0)";

        $notificationStmt = mysqli_prepare($link, $notificationQuery);

        if ($notificationStmt) {
            mysqli_stmt_bind_param($notificationStmt, "i", $userId);
            mysqli_stmt_execute($notificationStmt);
            StripeLogger::log(StripeLogLevel::INFO, "DOWNGRADE NOTIFICATION SENT - User ID: $userId");
        }

        // If there are any specific actions needed when a user downgrades
        // For example, you might need to:
        // 1. Check if the user has used any premium features that should now be limited
        // 2. Adjust usage limits or quotas
        // 3. Handle any data that might need to be archived or modified

        // This is a placeholder for any immediate actions that should happen on downgrade
        // The actual implementation will depend on your application's requirements

    } catch (Exception $e) {
        // Just log the error but don't throw it to avoid disrupting the main flow
        StripeLogger::log(StripeLogLevel::ERROR, "FAILED TO HANDLE SUBSCRIPTION DOWNGRADE: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'fromLevel' => $fromLevel ?? 'unknown',
            'toLevel' => $toLevel ?? 'unknown'
        ]);
    }
}

/**
 * Handle subscription cancellation event
 *
 * @param object $subscription The subscription object from Stripe
 */
/**
 * Downgrade a subscription to a lower plan
 *
 * This function schedules a downgrade of an existing subscription to a lower-priced plan.
 * The change will take effect at the end of the current billing period.
 */
function downgradeSubscription() {
    global $link, $data;

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }

    // Validate required parameters
    if (!$data || !isset($data['subscriptionId']) || !isset($data['newPriceId'])) {
        echo json_encode(new ErrorResult("Missing required parameters: subscriptionId and newPriceId", 400));
        return;
    }

    $subscriptionId = $data['subscriptionId'];
    $newPriceId = $data['newPriceId'];

    // Get current subscription details for cooldown check
    $query = "SELECT * FROM stripe_user_subscriptions WHERE stripe_subscription_id = ? AND user_id = ?";
    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        echo json_encode(new ErrorResult("Database error: " . mysqli_error($link), 500));
        return;
    }

    mysqli_stmt_bind_param($stmt, "si", $subscriptionId, $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $currentSubscription = mysqli_fetch_assoc($result);

    if (!$currentSubscription) {
        echo json_encode(new ErrorResult("Subscription not found or does not belong to the user", 404));
        return;
    }

    // Get new product information for cooldown check
    $newProductQuery = "SELECT p.name FROM stripe_prices sp
                       JOIN stripe_products p ON sp.stripe_product_id = p.stripe_product_id
                       WHERE sp.stripe_price_id = ?";
    $newProductStmt = mysqli_prepare($link, $newProductQuery);

    if (!$newProductStmt) {
        echo json_encode(new ErrorResult("Database error: " . mysqli_error($link), 500));
        return;
    }

    mysqli_stmt_bind_param($newProductStmt, "s", $newPriceId);
    mysqli_stmt_execute($newProductStmt);
    $newProductResult = mysqli_stmt_get_result($newProductStmt);
    $newProductData = mysqli_fetch_assoc($newProductResult);

    if (!$newProductData) {
        echo json_encode(new ErrorResult("Invalid price ID", 400));
        return;
    }

    $currentLevel = determineSubscriptionLevel($currentSubscription['plan_name']);
    $newLevel = determineSubscriptionLevel($newProductData['name']);

    // Check if user is in cooldown period
    $cooldownCheck = checkSubscriptionCooldown($userId, 'downgrade', $currentLevel, $newLevel);

    if ($cooldownCheck) {
        // Get user's language preference
        $userLang = getSelectedLanguage();

        // Get cooldown message in user's language
        $message = getSubscriptionCooldownMessage($userLang, 'downgrade', [
            'lastChangeDate' => $cooldownCheck['last_change_date'],
            'daysRemaining' => $cooldownCheck['days_remaining'],
            'cooldownEnds' => $cooldownCheck['cooldown_ends']
        ]);

        echo json_encode(new ErrorResult($message, 429)); // 429 Too Many Requests
        return;
    }

    try {
        // Verify that the subscription belongs to the user
        $query = "SELECT * FROM stripe_user_subscriptions WHERE stripe_subscription_id = ? AND user_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "si", $subscriptionId, $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if (!$subscription) {
            throw new Exception("Subscription not found or does not belong to the user");
        }

        // Get the current subscription from Stripe
        $stripeSubscription = \Stripe\Subscription::retrieve($subscriptionId);

        // Get the subscription item ID (needed for the update)
        $subscriptionItemId = null;
        if (isset($stripeSubscription->items) &&
            isset($stripeSubscription->items->data) &&
            count($stripeSubscription->items->data) > 0) {
            $subscriptionItemId = $stripeSubscription->items->data[0]->id;
        }

        if (!$subscriptionItemId) {
            throw new Exception("Could not find subscription item ID");
        }

        // Get current product ID and level
        $currentPlanId = $subscription['plan_id'];
        $currentProductId = null;
        $currentProductQuery = "SELECT stripe_product_id, unit_amount FROM stripe_prices WHERE stripe_price_id = ?";
        $currentProductStmt = mysqli_prepare($link, $currentProductQuery);

        if (!$currentProductStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($currentProductStmt, "s", $currentPlanId);
        mysqli_stmt_execute($currentProductStmt);
        $currentProductResult = mysqli_stmt_get_result($currentProductStmt);
        $currentProductData = mysqli_fetch_assoc($currentProductResult);

        if (!$currentProductData) {
            throw new Exception("Could not find current product information");
        }

        $currentProductId = $currentProductData['stripe_product_id'];
        $currentAmount = $currentProductData['unit_amount'];

        // Get current product name
        $currentProductName = null;
        $currentProductNameQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
        $currentProductNameStmt = mysqli_prepare($link, $currentProductNameQuery);

        if (!$currentProductNameStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($currentProductNameStmt, "s", $currentProductId);
        mysqli_stmt_execute($currentProductNameStmt);
        $currentProductNameResult = mysqli_stmt_get_result($currentProductNameStmt);
        $currentProductNameData = mysqli_fetch_assoc($currentProductNameResult);

        if ($currentProductNameData) {
            $currentProductName = $currentProductNameData['name'];
        }

        // Get new product information
        $newProductQuery = "SELECT sp.stripe_product_id, p.name, sp.unit_amount
                           FROM stripe_prices sp
                           JOIN stripe_products p ON sp.stripe_product_id = p.stripe_product_id
                           WHERE sp.stripe_price_id = ?";
        $newProductStmt = mysqli_prepare($link, $newProductQuery);

        if (!$newProductStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($newProductStmt, "s", $newPriceId);
        mysqli_stmt_execute($newProductStmt);
        $newProductResult = mysqli_stmt_get_result($newProductStmt);
        $newProductData = mysqli_fetch_assoc($newProductResult);

        if (!$newProductData) {
            throw new Exception("Could not find new product information");
        }

        $newProductId = $newProductData['stripe_product_id'];
        $newProductName = $newProductData['name'];
        $newAmount = $newProductData['unit_amount'];

        // Verify this is actually a downgrade (new plan is cheaper than current plan)
        if ($newAmount >= $currentAmount) {
            throw new Exception("The selected plan is not a downgrade. Please use the upgrade-subscription endpoint instead.");
        }

        // Schedule the downgrade to take effect at the end of the current billing period
        $updatedSubscription = \Stripe\Subscription::update(
            $subscriptionId,
            [
                'items' => [
                    [
                        'id' => $subscriptionItemId,
                        'price' => $newPriceId,
                    ],
                ],
                'proration_behavior' => 'none', // No proration - change takes effect at the end of the billing period
                'metadata' => [
                    'user_id' => $userId,
                    'downgrade_date' => date('Y-m-d H:i:s'),
                    'previous_plan' => $currentProductName,
                    'new_plan' => $newProductName
                ]
            ]
        );

        // Log the downgrade
        StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION MANUALLY DOWNGRADED - User ID: $userId, Subscription ID: $subscriptionId, From: $currentProductName, To: $newProductName");

        // Get user email and send downgrade confirmation email
        $userQuery = "SELECT u.email FROM users u WHERE u.id = ?";
        $userStmt = mysqli_prepare($link, $userQuery);

        if ($userStmt) {
            mysqli_stmt_bind_param($userStmt, "i", $userId);
            mysqli_stmt_execute($userStmt);
            $userResult = mysqli_stmt_get_result($userStmt);
            $userData = mysqli_fetch_assoc($userResult);

            if ($userData && isset($userData['email'])) {
                // Get effective date (end of current billing period)
                $effectiveDate = date('Y-m-d', $stripeSubscription->current_period_end);

                // Send downgrade confirmation email
                sendSubscriptionDowngradeEmail(
                    $userId,
                    $userData['email'],
                    $currentProductName,
                    $newProductName,
                    $effectiveDate
                );
            }
        }

        // The database update will be handled by the webhook event handler
        // when Stripe sends the customer.subscription.updated event

        // Return success response with the updated subscription details
        echo json_encode(new SuccessResult([
            'downgraded' => true,
            'message' => 'Your subscription has been scheduled to downgrade to ' . $newProductName . ' at the end of your current billing period on ' . date('Y-m-d', $stripeSubscription->current_period_end) . '.',
            'subscription' => [
                'id' => $subscription['id'],
                'stripe_subscription_id' => $subscriptionId,
                'current_plan' => $currentProductName,
                'new_plan' => $newProductName,
                'effective_date' => date('Y-m-d', $stripeSubscription->current_period_end),
                'status' => $updatedSubscription->status,
                'current_period_end' => date('Y-m-d H:i:s', $updatedSubscription->current_period_end)
            ]
        ]));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION DOWNGRADE ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'subscriptionId' => $subscriptionId ?? 'unknown',
            'newPriceId' => $newPriceId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Upgrade a subscription to a higher plan with prorated billing
 *
 * This function updates an existing subscription to a new price/plan
 * and ensures proper proration of the cost based on the remaining time
 * in the current billing period.
 */
function upgradeSubscription() {
    global $link, $data;

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }

    // Validate required parameters
    if (!$data || !isset($data['subscriptionId']) || !isset($data['newPriceId'])) {
        echo json_encode(new ErrorResult("Missing required parameters: subscriptionId and newPriceId", 400));
        return;
    }

    $subscriptionId = $data['subscriptionId'];
    $newPriceId = $data['newPriceId'];

    // Get current subscription details for cooldown check
    $query = "SELECT * FROM stripe_user_subscriptions WHERE stripe_subscription_id = ? AND user_id = ?";
    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        echo json_encode(new ErrorResult("Database error: " . mysqli_error($link), 500));
        return;
    }

    mysqli_stmt_bind_param($stmt, "si", $subscriptionId, $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $currentSubscription = mysqli_fetch_assoc($result);

    if (!$currentSubscription) {
        echo json_encode(new ErrorResult("Subscription not found or does not belong to the user", 404));
        return;
    }

    // Get new product information for cooldown check
    $newProductQuery = "SELECT p.name FROM stripe_prices sp
                       JOIN stripe_products p ON sp.stripe_product_id = p.stripe_product_id
                       WHERE sp.stripe_price_id = ?";
    $newProductStmt = mysqli_prepare($link, $newProductQuery);

    if (!$newProductStmt) {
        echo json_encode(new ErrorResult("Database error: " . mysqli_error($link), 500));
        return;
    }

    mysqli_stmt_bind_param($newProductStmt, "s", $newPriceId);
    mysqli_stmt_execute($newProductStmt);
    $newProductResult = mysqli_stmt_get_result($newProductStmt);
    $newProductData = mysqli_fetch_assoc($newProductResult);

    if (!$newProductData) {
        echo json_encode(new ErrorResult("Invalid price ID", 400));
        return;
    }

    $currentLevel = determineSubscriptionLevel($currentSubscription['plan_name']);
    $newLevel = determineSubscriptionLevel($newProductData['name']);

    // Check if user is in cooldown period
    $cooldownCheck = checkSubscriptionCooldown($userId, 'upgrade', $currentLevel, $newLevel);

    if ($cooldownCheck) {
        // Get user's language preference
        $userLang = getSelectedLanguage();

        // Get cooldown message in user's language
        $message = getSubscriptionCooldownMessage($userLang, 'upgrade', [
            'lastChangeDate' => $cooldownCheck['last_change_date'],
            'daysRemaining' => $cooldownCheck['days_remaining'],
            'cooldownEnds' => $cooldownCheck['cooldown_ends']
        ]);

        echo json_encode(new ErrorResult($message, 429)); // 429 Too Many Requests
        return;
    }

    try {
        // Verify that the subscription belongs to the user
        $query = "SELECT * FROM stripe_user_subscriptions WHERE stripe_subscription_id = ? AND user_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "si", $subscriptionId, $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if (!$subscription) {
            throw new Exception("Subscription not found or does not belong to the user");
        }

        // Get the current subscription from Stripe
        $stripeSubscription = \Stripe\Subscription::retrieve($subscriptionId);

        // Get the subscription item ID (needed for the update)
        $subscriptionItemId = null;
        if (isset($stripeSubscription->items) &&
            isset($stripeSubscription->items->data) &&
            count($stripeSubscription->items->data) > 0) {
            $subscriptionItemId = $stripeSubscription->items->data[0]->id;
        }

        if (!$subscriptionItemId) {
            throw new Exception("Could not find subscription item ID");
        }

        // Update the subscription with the new price
        $updatedSubscription = \Stripe\Subscription::update(
            $subscriptionId,
            [
                'items' => [
                    [
                        'id' => $subscriptionItemId,
                        'price' => $newPriceId,
                    ],
                ],
                'proration_behavior' => 'create_prorations', // This is the key setting for prorated billing
                'metadata' => [
                    'user_id' => $userId,
                    'upgrade_date' => date('Y-m-d H:i:s')
                ]
            ]
        );

        // Log the upgrade
        StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION MANUALLY UPGRADED - User ID: $userId, Subscription ID: $subscriptionId, New Price ID: $newPriceId");

        // Get user email and send upgrade confirmation email
        $userQuery = "SELECT u.email FROM users u WHERE u.id = ?";
        $userStmt = mysqli_prepare($link, $userQuery);

        if ($userStmt) {
            mysqli_stmt_bind_param($userStmt, "i", $userId);
            mysqli_stmt_execute($userStmt);
            $userResult = mysqli_stmt_get_result($userStmt);
            $userData = mysqli_fetch_assoc($userResult);

            if ($userData && isset($userData['email'])) {
                // Get old and new plan details for the email
                $oldPlanQuery = "SELECT p.name as product_name, p.unit_amount
                                FROM stripe_prices p
                                JOIN stripe_user_subscriptions s ON s.stripe_price_id = p.stripe_price_id
                                WHERE s.stripe_subscription_id = ?";
                $oldPlanStmt = mysqli_prepare($link, $oldPlanQuery);

                if ($oldPlanStmt) {
                    mysqli_stmt_bind_param($oldPlanStmt, "s", $subscriptionId);
                    mysqli_stmt_execute($oldPlanStmt);
                    $oldPlanResult = mysqli_stmt_get_result($oldPlanStmt);
                    $oldPlanData = mysqli_fetch_assoc($oldPlanResult);

                    $oldProductName = $oldPlanData['product_name'] ?? 'Previous Plan';
                    $oldAmount = $oldPlanData['unit_amount'] ?? 0;

                    // Get new plan details
                    $newPlanQuery = "SELECT name as product_name, unit_amount FROM stripe_prices WHERE stripe_price_id = ?";
                    $newPlanStmt = mysqli_prepare($link, $newPlanQuery);

                    if ($newPlanStmt) {
                        mysqli_stmt_bind_param($newPlanStmt, "s", $newPriceId);
                        mysqli_stmt_execute($newPlanStmt);
                        $newPlanResult = mysqli_stmt_get_result($newPlanStmt);
                        $newPlanData = mysqli_fetch_assoc($newPlanResult);

                        $newProductName = $newPlanData['product_name'] ?? 'New Plan';
                        $newAmount = $newPlanData['unit_amount'] ?? 0;

                        // Format amounts for display (convert from cents to dollars/TL)
                        $formattedOldAmount = number_format($oldAmount / 100, 2);
                        $formattedNewAmount = number_format($newAmount / 100, 2);

                        // Get currency from Stripe subscription
                        $currency = 'usd'; // Default currency
                        if (isset($updatedSubscription->plan) && isset($updatedSubscription->plan->currency)) {
                            $currency = $updatedSubscription->plan->currency;
                        }

                        // Get next billing date
                        $nextBillingDate = date('Y-m-d', $updatedSubscription->current_period_end ?? (time() + (30 * 24 * 60 * 60)));

                        // Send upgrade confirmation email
                        sendSubscriptionUpgradeEmail(
                            $userId,
                            $userData['email'],
                            $oldProductName,
                            $newProductName,
                            $formattedOldAmount,
                            $formattedNewAmount,
                            $currency,
                            $nextBillingDate
                        );
                    }
                }
            }
        }

        // The database update will be handled by the webhook event handler
        // when Stripe sends the customer.subscription.updated event

        // Return success response with the updated subscription details
        echo json_encode(new SuccessResult([
            'upgraded' => true,
            'message' => 'Subscription upgraded successfully. You will be charged the prorated amount for the remainder of your billing period.',
            'subscription' => [
                'id' => $subscription['id'],
                'stripe_subscription_id' => $subscriptionId,
                'status' => $updatedSubscription->status,
                'current_period_end' => date('Y-m-d H:i:s', $updatedSubscription->current_period_end)
            ]
        ]));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION UPGRADE ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'subscriptionId' => $subscriptionId ?? 'unknown',
            'newPriceId' => $newPriceId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

function handleCanceledSubscription($subscription) {
    global $link;

    try {
        $subscriptionId = $subscription->id;
        $stripeCustomerId = $subscription->customer;
        $canceledAt = date('Y-m-d H:i:s', $subscription->canceled_at ?? time());

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION CANCELED - Customer: $stripeCustomerId, Subscription ID: $subscriptionId");

        // 1. First check metadata for user ID
        $userId = null;
        if (isset($subscription->metadata) && isset($subscription->metadata->user_id)) {
            $metadataUserId = $subscription->metadata->user_id;

            // Find user by metadata user_id
            $query = "SELECT id FROM users WHERE id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "i", $metadataUserId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY METADATA IN CANCELLATION - User ID: $userId");
            }
        }

        // 2. If no user ID in metadata or not found, try to find by stripe_customer_id
        if (!$userId) {
            $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $stripeCustomerId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CUSTOMER ID IN CANCELLATION - User ID: $userId");
            }
        }

        // Update subscription in database
        $query = "UPDATE stripe_user_subscriptions SET
                status = 'canceled',
                canceled_at = ?,
                updated_at = NOW()
                WHERE stripe_subscription_id = ?";

        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "ss", $canceledAt, $subscriptionId);

        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception("Failed to update canceled subscription: " . mysqli_error($link));
        }

        $affectedRows = mysqli_affected_rows($link);

        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION CANCELED IN DB - Subscription ID: $subscriptionId, Canceled At: $canceledAt");

            // 3. If user not found, try to find by subscription ID
            if (!$userId) {
                $userQuery = "SELECT user_id FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
                $userStmt = mysqli_prepare($link, $userQuery);

                if (!$userStmt) {
                    throw new Exception("Database error: " . mysqli_error($link));
                }

                mysqli_stmt_bind_param($userStmt, "s", $subscriptionId);
                mysqli_stmt_execute($userStmt);
                $result = mysqli_stmt_get_result($userStmt);
                $subscriptionData = mysqli_fetch_assoc($result);

                if ($subscriptionData) {
                    $userId = $subscriptionData['user_id'];
                    StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY SUBSCRIPTION ID IN CANCELLATION - User ID: $userId");
                }
            }

            // If user found, check subscription status
            if ($userId) {
                // Check if user has other active subscriptions
                $checkQuery = "SELECT COUNT(*) as active_count FROM stripe_user_subscriptions
                        WHERE user_id = ? AND status = 'active' AND stripe_subscription_id != ?";
                $checkStmt = mysqli_prepare($link, $checkQuery);

                if (!$checkStmt) {
                    throw new Exception("Database error: " . mysqli_error($link));
                }

                mysqli_stmt_bind_param($checkStmt, "is", $userId, $subscriptionId);
                mysqli_stmt_execute($checkStmt);
                $checkResult = mysqli_stmt_get_result($checkStmt);
                $row = mysqli_fetch_assoc($checkResult);

                // If no other active subscriptions, set user level to 'free'
                if ($row['active_count'] == 0) {
                    $updateUserQuery = "UPDATE users SET subscription_level = 'free' WHERE id = ?";
                    $updateUserStmt = mysqli_prepare($link, $updateUserQuery);

                    if (!$updateUserStmt) {
                        throw new Exception("Database error: " . mysqli_error($link));
                    }

                    mysqli_stmt_bind_param($updateUserStmt, "i", $userId);

                    if (mysqli_stmt_execute($updateUserStmt)) {
                        StripeLogger::log(StripeLogLevel::INFO, "STRIPE USER SUBSCRIPTION LEVEL SET TO FREE - User ID: $userId");

                        // Get user email and subscription details for cancellation email
                        $userQuery = "SELECT u.email, sus.plan_name, sus.canceled_at
                                     FROM users u
                                     JOIN stripe_user_subscriptions sus ON sus.user_id = u.id
                                     WHERE sus.stripe_subscription_id = ?";
                        $userStmt = mysqli_prepare($link, $userQuery);

                        if ($userStmt) {
                            mysqli_stmt_bind_param($userStmt, "s", $subscriptionId);
                            mysqli_stmt_execute($userStmt);
                            $userResult = mysqli_stmt_get_result($userStmt);
                            $userData = mysqli_fetch_assoc($userResult);

                            if ($userData && isset($userData['email'])) {
                                // Send cancellation email
                                $endDate = date('Y-m-d');

                                sendSubscriptionCancellationEmail(
                                    $userId,
                                    $userData['email'],
                                    $userData['plan_name'] ?? 'Premium Plan',
                                    $endDate,
                                    true // Immediate cancellation
                                );
                            }
                        }
                    } else {
                        StripeLogger::log(StripeLogLevel::ERROR, "FAILED TO UPDATE USER SUBSCRIPTION LEVEL: " . mysqli_error($link), [
                            'userId' => $userId ?? 'unknown'
                        ]);
                    }
                } else {
                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE USER HAS OTHER ACTIVE SUBSCRIPTIONS - User ID: $userId, Active Count: " . $row['active_count']);
                }
            } else {
                StripeLogger::log(StripeLogLevel::WARNING, "STRIPE NO USER FOUND FOR CANCELED SUBSCRIPTION - Subscription ID: $subscriptionId");
            }
        } else {
            StripeLogger::log(StripeLogLevel::WARNING, "STRIPE NO SUBSCRIPTION FOUND TO CANCEL - Subscription ID: $subscriptionId");
        }

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION CANCELLATION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'subscriptionId' => $subscriptionId ?? 'unknown',
            'userId' => $userId ?? 'unknown'
        ]);
        throw $e;
    }
}

// We're using getSelectedLanguage() from language_config.php instead of defining our own function
