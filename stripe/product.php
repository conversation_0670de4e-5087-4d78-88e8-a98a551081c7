<?php
/**
 * Stripe Product Functions
 *
 * This file contains functions related to Stripe products.
 */

// Include required files
require_once __DIR__ . '/StripeLogger.php';

/**
 * Handle product created event
 *
 * @param object $product The product object from Stripe
 */
function handleProductCreated($product) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRODUCT CREATED - ID: {$product->id}, Name: {$product->name}");

    try {
        // Convert metadata to JSON
        $metadata = json_encode($product->metadata);

        // Convert images to JSON
        $images = json_encode($product->images);

        // Check if product already exists
        $checkQuery = "SELECT id FROM stripe_products WHERE stripe_product_id = ?";
        $checkStmt = mysqli_prepare($link, $checkQuery);

        if (!$checkStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($checkStmt, "s", $product->id);
        mysqli_stmt_execute($checkStmt);
        $result = mysqli_stmt_get_result($checkStmt);

        if (mysqli_num_rows($result) > 0) {
            // Product already exists, update it
            $updateQuery = "UPDATE stripe_products SET
                name = ?,
                description = ?,
                active = ?,
                metadata = ?,
                images = ?
                WHERE stripe_product_id = ?";

            $updateStmt = mysqli_prepare($link, $updateQuery);

            if (!$updateStmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            $active = $product->active ? 1 : 0;

            mysqli_stmt_bind_param(
                $updateStmt,
                "ssisss",
                $product->name,
                $product->description,
                $active,
                $metadata,
                $images,
                $product->id
            );

            if (!mysqli_stmt_execute($updateStmt)) {
                throw new Exception("Failed to update product: " . mysqli_error($link));
            }

            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRODUCT UPDATED IN DB - ID: {$product->id}");
        } else {
            // Insert new product
            $insertQuery = "INSERT INTO stripe_products (
                stripe_product_id,
                name,
                description,
                active,
                metadata,
                images
            ) VALUES (?, ?, ?, ?, ?, ?)";

            $insertStmt = mysqli_prepare($link, $insertQuery);

            if (!$insertStmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            $active = $product->active ? 1 : 0;

            mysqli_stmt_bind_param(
                $insertStmt,
                "sssiss",
                $product->id,
                $product->name,
                $product->description,
                $active,
                $metadata,
                $images
            );

            if (!mysqli_stmt_execute($insertStmt)) {
                throw new Exception("Failed to insert product: " . mysqli_error($link));
            }

            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRODUCT INSERTED INTO DB - ID: {$product->id}");
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PRODUCT CREATION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'productId' => $product->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle product updated event
 *
 * @param object $product The product object from Stripe
 */
function handleProductUpdated($product) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRODUCT UPDATED - ID: {$product->id}, Name: {$product->name}");

    try {
        // Convert metadata to JSON
        $metadata = json_encode($product->metadata);

        // Convert images to JSON
        $images = json_encode($product->images);

        // Update product in database
        $updateQuery = "UPDATE stripe_products SET
            name = ?,
            description = ?,
            active = ?,
            metadata = ?,
            images = ?
            WHERE stripe_product_id = ?";

        $updateStmt = mysqli_prepare($link, $updateQuery);

        if (!$updateStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        $active = $product->active ? 1 : 0;

        mysqli_stmt_bind_param(
            $updateStmt,
            "ssisss",
            $product->name,
            $product->description,
            $active,
            $metadata,
            $images,
            $product->id
        );

        if (!mysqli_stmt_execute($updateStmt)) {
            throw new Exception("Failed to update product: " . mysqli_error($link));
        }

        $affectedRows = mysqli_affected_rows($link);

        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRODUCT UPDATED IN DB - ID: {$product->id}");
        } else {
            // Product doesn't exist, create it
            handleProductCreated($product);
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PRODUCT UPDATE ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'productId' => $product->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle product deleted event
 *
 * @param object $product The product object from Stripe
 */
function handleProductDeleted($product) {
    global $link;

    StripeLogger::log(StripeLogLevel::WARNING, "STRIPE PRODUCT DELETED - ID: {$product->id}, Name: {$product->name}");

    try {
        // Delete product from database
        $deleteQuery = "DELETE FROM stripe_products WHERE stripe_product_id = ?";
        $deleteStmt = mysqli_prepare($link, $deleteQuery);

        if (!$deleteStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($deleteStmt, "s", $product->id);

        if (!mysqli_stmt_execute($deleteStmt)) {
            throw new Exception("Failed to delete product: " . mysqli_error($link));
        }

        $affectedRows = mysqli_affected_rows($link);

        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRODUCT DELETED FROM DB - ID: {$product->id}");
        } else {
            StripeLogger::log(StripeLogLevel::NOTICE, "STRIPE PRODUCT NOT FOUND IN DB - ID: {$product->id}");
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PRODUCT DELETION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'productId' => $product->id ?? 'unknown'
        ]);
    }
}
