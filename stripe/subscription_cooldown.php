<?php
/**
 * Stripe Subscription Cooldown Functions
 *
 * This file contains functions related to managing subscription change cooldown periods.
 */

// Include required files
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../secrets.php';
require_once __DIR__ . '/../utils.php';
require_once __DIR__ . '/../models/ResultModel.php';
require_once __DIR__ . '/StripeLogger.php';

/**
 * Check if a user is in a cooldown period for subscription changes
 *
 * @param int $userId User ID
 * @param string $changeType Type of change (upgrade, downgrade, any)
 * @param string $fromLevel Current subscription level
 * @param string $toLevel Target subscription level
 * @return array|false Returns false if no cooldown, otherwise returns an array with cooldown info
 */
function checkSubscriptionCooldown($userId, $changeType, $fromLevel, $toLevel) {
    global $link;

    try {
        // Get the most recent subscription change of the same type for this user
        // Only check for cooldown if the user is trying to do the same type of change again
        $recentChangeQuery = "SELECT * FROM stripe_subscription_changes
                             WHERE user_id = ? AND change_type = ?
                             ORDER BY created_at DESC
                             LIMIT 1";
        $recentChangeStmt = mysqli_prepare($link, $recentChangeQuery);

        if (!$recentChangeStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($recentChangeStmt, "is", $userId, $changeType);
        mysqli_stmt_execute($recentChangeStmt);
        $recentChangeResult = mysqli_stmt_get_result($recentChangeStmt);
        $recentChange = mysqli_fetch_assoc($recentChangeResult);

        // If no recent changes, no cooldown applies
        if (!$recentChange) {
            return false;
        }

        // Get the applicable cooldown configuration
        // Try to find the most specific rule first, then fall back to more general rules

        // Log the search parameters
        StripeLogger::log(StripeLogLevel::DEBUG, "SEARCHING FOR COOLDOWN CONFIG", [
            'userId' => $userId,
            'changeType' => $changeType,
            'fromLevel' => $fromLevel,
            'toLevel' => $toLevel
        ]);

        // 1. First, try to find a rule specifically for this change type (upgrade/downgrade) with any levels
        $cooldownQuery = "SELECT * FROM stripe_subscription_cooldown_config
                         WHERE change_type = ? AND from_level = 'any' AND to_level = 'any'
                         LIMIT 1";
        $cooldownStmt = mysqli_prepare($link, $cooldownQuery);
        mysqli_stmt_bind_param($cooldownStmt, "s", $changeType);
        mysqli_stmt_execute($cooldownStmt);
        $cooldownResult = mysqli_stmt_get_result($cooldownStmt);
        $cooldownConfig = mysqli_fetch_assoc($cooldownResult);

        if ($cooldownConfig) {
            StripeLogger::log(StripeLogLevel::DEBUG, "FOUND CHANGE TYPE SPECIFIC COOLDOWN CONFIG", [
                'configId' => $cooldownConfig['id'],
                'changeType' => $cooldownConfig['change_type'],
                'fromLevel' => $cooldownConfig['from_level'],
                'toLevel' => $cooldownConfig['to_level'],
                'cooldownDays' => $cooldownConfig['cooldown_days']
            ]);
        }

        // 2. If no rule found, try with specific from and to levels
        if (!$cooldownConfig) {
            $cooldownQuery = "SELECT * FROM stripe_subscription_cooldown_config
                             WHERE change_type = ? AND from_level = ? AND to_level = ?
                             LIMIT 1";
            $cooldownStmt = mysqli_prepare($link, $cooldownQuery);
            mysqli_stmt_bind_param($cooldownStmt, "sss", $changeType, $fromLevel, $toLevel);
            mysqli_stmt_execute($cooldownStmt);
            $cooldownResult = mysqli_stmt_get_result($cooldownStmt);
            $cooldownConfig = mysqli_fetch_assoc($cooldownResult);

            if ($cooldownConfig) {
                StripeLogger::log(StripeLogLevel::DEBUG, "FOUND SPECIFIC LEVEL COOLDOWN CONFIG", [
                    'configId' => $cooldownConfig['id'],
                    'changeType' => $cooldownConfig['change_type'],
                    'fromLevel' => $cooldownConfig['from_level'],
                    'toLevel' => $cooldownConfig['to_level'],
                    'cooldownDays' => $cooldownConfig['cooldown_days']
                ]);
            }
        }

        // 3. If still no rule, try with 'any' for to_level
        if (!$cooldownConfig) {
            $cooldownQuery = "SELECT * FROM stripe_subscription_cooldown_config
                             WHERE change_type = ? AND from_level = ? AND to_level = 'any'
                             LIMIT 1";
            $cooldownStmt = mysqli_prepare($link, $cooldownQuery);
            mysqli_stmt_bind_param($cooldownStmt, "ss", $changeType, $fromLevel);
            mysqli_stmt_execute($cooldownStmt);
            $cooldownResult = mysqli_stmt_get_result($cooldownStmt);
            $cooldownConfig = mysqli_fetch_assoc($cooldownResult);

            if ($cooldownConfig) {
                StripeLogger::log(StripeLogLevel::DEBUG, "FOUND FROM LEVEL SPECIFIC COOLDOWN CONFIG", [
                    'configId' => $cooldownConfig['id'],
                    'changeType' => $cooldownConfig['change_type'],
                    'fromLevel' => $cooldownConfig['from_level'],
                    'toLevel' => $cooldownConfig['to_level'],
                    'cooldownDays' => $cooldownConfig['cooldown_days']
                ]);
            }
        }

        // 4. If still no rule, try with 'any' for from_level
        if (!$cooldownConfig) {
            $cooldownQuery = "SELECT * FROM stripe_subscription_cooldown_config
                             WHERE change_type = ? AND from_level = 'any' AND to_level = ?
                             LIMIT 1";
            $cooldownStmt = mysqli_prepare($link, $cooldownQuery);
            mysqli_stmt_bind_param($cooldownStmt, "ss", $changeType, $toLevel);
            mysqli_stmt_execute($cooldownStmt);
            $cooldownResult = mysqli_stmt_get_result($cooldownStmt);
            $cooldownConfig = mysqli_fetch_assoc($cooldownResult);

            if ($cooldownConfig) {
                StripeLogger::log(StripeLogLevel::DEBUG, "FOUND TO LEVEL SPECIFIC COOLDOWN CONFIG", [
                    'configId' => $cooldownConfig['id'],
                    'changeType' => $cooldownConfig['change_type'],
                    'fromLevel' => $cooldownConfig['from_level'],
                    'toLevel' => $cooldownConfig['to_level'],
                    'cooldownDays' => $cooldownConfig['cooldown_days']
                ]);
            }
        }

        // 5. If still no rule, try with default 'any' for all fields
        if (!$cooldownConfig) {
            $cooldownQuery = "SELECT * FROM stripe_subscription_cooldown_config
                             WHERE change_type = 'any' AND from_level = 'any' AND to_level = 'any'
                             LIMIT 1";
            $cooldownStmt = mysqli_prepare($link, $cooldownQuery);
            mysqli_stmt_execute($cooldownStmt);
            $cooldownResult = mysqli_stmt_get_result($cooldownStmt);
            $cooldownConfig = mysqli_fetch_assoc($cooldownResult);

            if ($cooldownConfig) {
                StripeLogger::log(StripeLogLevel::DEBUG, "FOUND DEFAULT COOLDOWN CONFIG", [
                    'configId' => $cooldownConfig['id'],
                    'changeType' => $cooldownConfig['change_type'],
                    'fromLevel' => $cooldownConfig['from_level'],
                    'toLevel' => $cooldownConfig['to_level'],
                    'cooldownDays' => $cooldownConfig['cooldown_days']
                ]);
            }
        }

        // If no cooldown config found, no cooldown applies
        if (!$cooldownConfig) {
            return false;
        }

        // Calculate if the user is still in cooldown period
        $cooldownDays = $cooldownConfig['cooldown_days'];

        // Log the cooldown configuration being used
        StripeLogger::log(StripeLogLevel::DEBUG, "SUBSCRIPTION COOLDOWN CONFIG FOUND", [
            'userId' => $userId,
            'changeType' => $changeType,
            'fromLevel' => $fromLevel,
            'toLevel' => $toLevel,
            'cooldownDays' => $cooldownDays,
            'configId' => $cooldownConfig['id'],
            'configChangeType' => $cooldownConfig['change_type'],
            'configFromLevel' => $cooldownConfig['from_level'],
            'configToLevel' => $cooldownConfig['to_level']
        ]);

        // If cooldown days is 0, no cooldown applies (immediate changes allowed)
        if ($cooldownDays <= 0) {
            return false;
        }

        $lastChangeDate = new DateTime($recentChange['created_at']);
        $now = new DateTime();
        $daysSinceLastChange = $now->diff($lastChangeDate)->days;

        // If the cooldown period has passed, no cooldown applies
        if ($daysSinceLastChange >= $cooldownDays) {
            return false;
        }

        // User is in cooldown period
        $cooldownEndsDate = clone $lastChangeDate;
        $cooldownEndsDate->add(new DateInterval("P{$cooldownDays}D"));

        return [
            'in_cooldown' => true,
            'last_change_date' => $lastChangeDate->format('Y-m-d H:i:s'),
            'cooldown_days' => $cooldownDays,
            'days_since_last_change' => $daysSinceLastChange,
            'days_remaining' => $cooldownDays - $daysSinceLastChange,
            'cooldown_ends' => $cooldownEndsDate->format('Y-m-d H:i:s'),
            'change_type' => $recentChange['change_type'],
            'from_level' => $recentChange['from_level'],
            'to_level' => $recentChange['to_level']
        ];

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "SUBSCRIPTION COOLDOWN CHECK ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'changeType' => $changeType ?? 'unknown',
            'fromLevel' => $fromLevel ?? 'unknown',
            'toLevel' => $toLevel ?? 'unknown'
        ]);

        // In case of error, don't block the user from changing subscription
        return false;
    }
}
