<?php
/**
 * Stripe Payment Functions
 *
 * This file contains functions related to Stripe payments.
 */

// Include required files
require_once __DIR__ . '/StripeLogger.php';

/**
 * Handle successful payment event
 *
 * @param object $paymentIntent The payment intent object from Stripe
 */
function handleSuccessfulPayment($paymentIntent) {
    global $link;

    // Process successful payment
    $paymentIntentId = $paymentIntent->id;
    $amount = $paymentIntent->amount;
    $currency = $paymentIntent->currency;
    $customer = $paymentIntent->customer;
    $paymentMethod = $paymentIntent->payment_method;
    $created = date('Y-m-d H:i:s', $paymentIntent->created);

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PAYMENT SUCCESS - Amount: $amount $currency - Customer: $customer, Payment ID: $paymentIntentId");

    try {
        // Find user by stripe_customer_id
        $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "s", $customer);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if (!$user) {
            StripeLogger::log(StripeLogLevel::WARNING, "No user found for customer ID: $customer");
            return;
        }

        $userId = $user['id'];

        // Save payment information to database
        $insertQuery = "INSERT INTO stripe_payment_history (
            user_id,
            stripe_customer_id,
            stripe_payment_intent_id,
            payment_method_id,
            amount,
            currency,
            status,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'succeeded', ?)";

        $insertStmt = mysqli_prepare($link, $insertQuery);

        if (!$insertStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        // Calculate amount in dollars before binding
        $amountInDollars = $amount / 100; // Stripe amounts are in cents

        mysqli_stmt_bind_param(
            $insertStmt,
            "isssdss",
            $userId,
            $customer,
            $paymentIntentId,
            $paymentMethod,
            $amountInDollars,
            $currency,
            $created
        );

        if (!mysqli_stmt_execute($insertStmt)) {
            throw new Exception("Failed to save payment: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "Payment recorded for user ID: $userId");

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "Error processing successful payment: " . $e->getMessage(), [
            'exception' => get_class($e),
            'paymentIntentId' => $paymentIntentId ?? 'unknown',
            'customer' => $customer ?? 'unknown'
        ]);
    }
}

/**
 * Handle failed payment event
 *
 * @param object $paymentIntent The payment intent object from Stripe
 */
function handleFailedPayment($paymentIntent) {
    global $link;

    $paymentIntentId = $paymentIntent->id;
    $error = $paymentIntent->last_payment_error;
    $customer = $paymentIntent->customer;
    $amount = $paymentIntent->amount;
    $currency = $paymentIntent->currency;
    $created = date('Y-m-d H:i:s', $paymentIntent->created);
    $errorMessage = $error ? $error->message : 'Unknown error';
    $errorCode = $error ? $error->code : 'unknown';

    StripeLogger::log(StripeLogLevel::WARNING, "STRIPE PAYMENT FAILED - Customer: $customer, Payment ID: $paymentIntentId, Error: " . json_encode($error));

    try {
        // Find user by stripe_customer_id
        $query = "SELECT id, email FROM users WHERE stripe_customer_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "s", $customer);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if (!$user) {
            StripeLogger::log(StripeLogLevel::WARNING, "No user found for customer ID: $customer");
            return;
        }

        $userId = $user['id'];

        // Save failed payment information to database
        $insertQuery = "INSERT INTO stripe_payment_history (
            user_id,
            stripe_customer_id,
            stripe_payment_intent_id,
            amount,
            currency,
            status,
            error_message,
            error_code,
            created_at
        ) VALUES (?, ?, ?, ?, ?, 'failed', ?, ?, ?)";

        $insertStmt = mysqli_prepare($link, $insertQuery);

        if (!$insertStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        // Calculate amount in dollars before binding
        $amountInDollars = $amount / 100; // Stripe amounts are in cents

        mysqli_stmt_bind_param(
            $insertStmt,
            "issdsss",
            $userId,
            $customer,
            $paymentIntentId,
            $amountInDollars,
            $currency,
            $errorMessage,
            $errorCode,
            $created
        );

        if (!mysqli_stmt_execute($insertStmt)) {
            throw new Exception("Failed to save failed payment: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "Failed payment recorded for user ID: $userId");

        // Send notification to user (via email or other methods)
        // This part can be customized according to your notification system

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "Error processing failed payment: " . $e->getMessage(), [
            'exception' => get_class($e),
            'paymentIntentId' => $paymentIntentId ?? 'unknown',
            'customer' => $customer ?? 'unknown',
            'errorCode' => $errorCode ?? 'unknown'
        ]);
    }
}
