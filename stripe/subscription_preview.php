<?php
/**
 * Stripe Subscription Preview Functions
 *
 * This file contains functions for previewing subscription changes before they are applied.
 */

// Include required files
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../secrets.php';
require_once __DIR__ . '/../utils.php';
require_once __DIR__ . '/../models/ResultModel.php';
require_once __DIR__ . '/StripeLogger.php';
require_once __DIR__ . '/language_strings.php';
require_once __DIR__ . '/../language_config.php';

// We're using getSelectedLanguage() from language_config.php instead of defining our own function

/**
 * Preview a subscription upgrade
 *
 * This function calculates the prorated amount that would be charged
 * if the user upgrades their subscription to a new plan.
 */
function previewSubscriptionUpgrade() {
    global $link, $data;

    StripeLogger::log(StripeLogLevel::INFO, "PREVIEW SUBSCRIPTION UPGRADE - Starting preview process");

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        StripeLogger::log(StripeLogLevel::ERROR, "PREVIEW SUBSCRIPTION UPGRADE - Authentication failed");
        return; // Error response already sent
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "PREVIEW SUBSCRIPTION UPGRADE - User authenticated", [
        'userId' => $userId
    ]);

    // Validate required parameters
    if (!$data || !isset($data['subscriptionId']) || !isset($data['newPriceId'])) {
        StripeLogger::log(StripeLogLevel::ERROR, "PREVIEW SUBSCRIPTION UPGRADE - Missing required parameters", [
            'data' => $data ?? 'null'
        ]);
        echo json_encode(new ErrorResult("Missing required parameters: subscriptionId and newPriceId", 400));
        return;
    }

    $subscriptionId = $data['subscriptionId'];
    $newPriceId = $data['newPriceId'];

    StripeLogger::log(StripeLogLevel::DEBUG, "PREVIEW SUBSCRIPTION UPGRADE - Parameters validated", [
        'userId' => $userId,
        'subscriptionId' => $subscriptionId,
        'newPriceId' => $newPriceId
    ]);

    try {
        // Verify that the subscription belongs to the user
        $query = "SELECT * FROM stripe_user_subscriptions WHERE stripe_subscription_id = ? AND user_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "si", $subscriptionId, $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if (!$subscription) {
            throw new Exception("Subscription not found or does not belong to the user");
        }

        // Get the current subscription from Stripe
        $stripeSubscription = \Stripe\Subscription::retrieve($subscriptionId);

        // Get the subscription item ID (needed for the update)
        $subscriptionItemId = null;
        if (isset($stripeSubscription->items) &&
            isset($stripeSubscription->items->data) &&
            count($stripeSubscription->items->data) > 0) {
            $subscriptionItemId = $stripeSubscription->items->data[0]->id;
        }

        if (!$subscriptionItemId) {
            throw new Exception("Could not find subscription item ID");
        }

        // Get current product ID and level
        $currentPlanId = $subscription['plan_id'];
        $currentProductId = null;
        $currentProductQuery = "SELECT stripe_product_id, unit_amount FROM stripe_prices WHERE stripe_price_id = ?";
        $currentProductStmt = mysqli_prepare($link, $currentProductQuery);

        if (!$currentProductStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($currentProductStmt, "s", $currentPlanId);
        mysqli_stmt_execute($currentProductStmt);
        $currentProductResult = mysqli_stmt_get_result($currentProductStmt);
        $currentProductData = mysqli_fetch_assoc($currentProductResult);

        if (!$currentProductData) {
            throw new Exception("Could not find current product information");
        }

        $currentProductId = $currentProductData['stripe_product_id'];
        $currentAmount = $currentProductData['unit_amount'];

        // Get current product name
        $currentProductName = null;
        $currentProductNameQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
        $currentProductNameStmt = mysqli_prepare($link, $currentProductNameQuery);

        if (!$currentProductNameStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($currentProductNameStmt, "s", $currentProductId);
        mysqli_stmt_execute($currentProductNameStmt);
        $currentProductNameResult = mysqli_stmt_get_result($currentProductNameStmt);
        $currentProductNameData = mysqli_fetch_assoc($currentProductNameResult);

        if ($currentProductNameData) {
            $currentProductName = $currentProductNameData['name'];
        }

        // Get new product information
        $newProductQuery = "SELECT sp.stripe_product_id, p.name, sp.unit_amount, sp.currency
                           FROM stripe_prices sp
                           JOIN stripe_products p ON sp.stripe_product_id = p.stripe_product_id
                           WHERE sp.stripe_price_id = ?";
        $newProductStmt = mysqli_prepare($link, $newProductQuery);

        if (!$newProductStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($newProductStmt, "s", $newPriceId);
        mysqli_stmt_execute($newProductStmt);
        $newProductResult = mysqli_stmt_get_result($newProductStmt);
        $newProductData = mysqli_fetch_assoc($newProductResult);

        if (!$newProductData) {
            throw new Exception("Could not find new product information");
        }

        $newProductId = $newProductData['stripe_product_id'];
        $newProductName = $newProductData['name'];
        $newAmount = $newProductData['unit_amount'];
        $currency = $newProductData['currency'];

        // Verify this is actually an upgrade (new plan is more expensive than current plan)
        if ($newAmount <= $currentAmount) {
            throw new Exception("The selected plan is not an upgrade. Please use the downgrade-subscription endpoint instead.");
        }

        // Create an invoice preview to calculate the prorated amount
        $invoicePreview = \Stripe\Invoice::upcoming([
            'customer' => $stripeSubscription->customer,
            'subscription' => $subscriptionId,
            'subscription_items' => [
                [
                    'id' => $subscriptionItemId,
                    'price' => $newPriceId,
                ],
            ],
            'subscription_proration_date' => time(),
            'subscription_proration_behavior' => 'create_prorations',
        ]);

        // Calculate the prorated amount
        $proratedAmount = 0;
        $lineItems = [];

        foreach ($invoicePreview->lines->data as $line) {
            if ($line->proration) {
                $proratedAmount += $line->amount;
                $lineItems[] = [
                    'description' => $line->description,
                    'amount' => $line->amount / 100, // Convert to dollars/euros
                    'currency' => $currency,
                    'period' => [
                        'start' => date('Y-m-d', $line->period->start),
                        'end' => date('Y-m-d', $line->period->end)
                    ]
                ];
            }
        }

        // Format the prorated amount
        $formattedProratedAmount = number_format($proratedAmount / 100, 2);

        // Get user's language preference
        $userLang = getSelectedLanguage();

        // Create detailed message based on language
        $message = getSubscriptionUpgradePreviewMessage($userLang, [
            'currentPlan' => $currentProductName,
            'newPlan' => $newProductName,
            'proratedAmount' => $formattedProratedAmount,
            'currency' => $currency,
            'nextBillingDate' => date('F j, Y', $stripeSubscription->current_period_end)
        ]);

        // Return the preview information
        echo json_encode(new SuccessResult([
            'current_plan' => [
                'name' => $currentProductName,
                'amount' => $currentAmount ,
                'currency' => $currency
            ],
            'new_plan' => [
                'name' => $newProductName,
                'amount' => $newAmount ,
                'currency' => $currency
            ],
            'proration' => [
                'total_amount' => $formattedProratedAmount,
                'currency' => $currency,
                'line_items' => $lineItems
            ],
            'next_billing_date' => date('Y-m-d', $stripeSubscription->current_period_end),
            'message' => $message
        ]));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION UPGRADE PREVIEW ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'subscriptionId' => $subscriptionId ?? 'unknown',
            'newPriceId' => $newPriceId ?? 'unknown'
        ]);

        // Get user's language preference
        $userLang = getSelectedLanguage();

        // Get error message in user's language
        $errorMessage = getSubscriptionUpgradeErrorMessage($userLang);

        // For debugging, add the actual error message (in production, you might want to remove this)
        $errorMessage .= " (Error: " . $e->getMessage() . ")";

        echo json_encode(new ErrorResult($errorMessage, 500));
    }
}

/**
 * Preview a subscription downgrade
 *
 * This function provides information about what would happen
 * if the user downgrades their subscription to a new plan.
 */
function previewSubscriptionDowngrade() {
    global $link, $data;

    StripeLogger::log(StripeLogLevel::INFO, "PREVIEW SUBSCRIPTION DOWNGRADE - Starting preview process");

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        StripeLogger::log(StripeLogLevel::ERROR, "PREVIEW SUBSCRIPTION DOWNGRADE - Authentication failed");
        return; // Error response already sent
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "PREVIEW SUBSCRIPTION DOWNGRADE - User authenticated", [
        'userId' => $userId
    ]);

    // Validate required parameters
    if (!$data || !isset($data['subscriptionId']) || !isset($data['newPriceId'])) {
        echo json_encode(new ErrorResult("Missing required parameters: subscriptionId and newPriceId", 400));
        return;
    }

    $subscriptionId = $data['subscriptionId'];
    $newPriceId = $data['newPriceId'];

    try {
        // Verify that the subscription belongs to the user
        $query = "SELECT * FROM stripe_user_subscriptions WHERE stripe_subscription_id = ? AND user_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "si", $subscriptionId, $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if (!$subscription) {
            throw new Exception("Subscription not found or does not belong to the user");
        }

        // Get the current subscription from Stripe
        $stripeSubscription = \Stripe\Subscription::retrieve($subscriptionId);

        // Get current product ID and level
        $currentPlanId = $subscription['plan_id'];
        $currentProductQuery = "SELECT sp.stripe_product_id, p.name, sp.unit_amount, sp.currency
                               FROM stripe_prices sp
                               JOIN stripe_products p ON sp.stripe_product_id = p.stripe_product_id
                               WHERE sp.stripe_price_id = ?";
        $currentProductStmt = mysqli_prepare($link, $currentProductQuery);

        if (!$currentProductStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($currentProductStmt, "s", $currentPlanId);
        mysqli_stmt_execute($currentProductStmt);
        $currentProductResult = mysqli_stmt_get_result($currentProductStmt);
        $currentProductData = mysqli_fetch_assoc($currentProductResult);

        if (!$currentProductData) {
            throw new Exception("Could not find current product information");
        }

        $currentProductName = $currentProductData['name'];
        $currentAmount = $currentProductData['unit_amount'];
        $currency = $currentProductData['currency'];

        // Get new product information
        $newProductQuery = "SELECT sp.stripe_product_id, p.name, sp.unit_amount
                           FROM stripe_prices sp
                           JOIN stripe_products p ON sp.stripe_product_id = p.stripe_product_id
                           WHERE sp.stripe_price_id = ?";
        $newProductStmt = mysqli_prepare($link, $newProductQuery);

        if (!$newProductStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($newProductStmt, "s", $newPriceId);
        mysqli_stmt_execute($newProductStmt);
        $newProductResult = mysqli_stmt_get_result($newProductStmt);
        $newProductData = mysqli_fetch_assoc($newProductResult);

        if (!$newProductData) {
            throw new Exception("Could not find new product information");
        }

        $newProductName = $newProductData['name'];
        $newAmount = $newProductData['unit_amount'];

        // Verify this is actually a downgrade (new plan is cheaper than current plan)
        if ($newAmount >= $currentAmount) {
            throw new Exception("The selected plan is not a downgrade. Please use the upgrade-subscription endpoint instead.");
        }

        // Calculate the next billing date
        $nextBillingDate = date('Y-m-d', $stripeSubscription->current_period_end);

        // Get user's language preference
        $userLang = getSelectedLanguage();

        // Create detailed message based on language
        $message = getSubscriptionDowngradePreviewMessage($userLang, [
            'currentPlan' => $currentProductName,
            'newPlan' => $newProductName,
            'effectiveDate' => date('F j, Y', strtotime($nextBillingDate))
        ]);

        // Return the preview information
        echo json_encode(new SuccessResult([
            'current_plan' => [
                'name' => $currentProductName,
                'amount' => $currentAmount ,
                'currency' => $currency
            ],
            'new_plan' => [
                'name' => $newProductName,
                'amount' => $newAmount ,
                'currency' => $currency
            ],
            'effective_date' => $nextBillingDate,
            'message' => $message
        ]));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION DOWNGRADE PREVIEW ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'subscriptionId' => $subscriptionId ?? 'unknown',
            'newPriceId' => $newPriceId ?? 'unknown'
        ]);

        // Get user's language preference
        $userLang = getSelectedLanguage();

        // Get error message in user's language
        $errorMessage = getSubscriptionDowngradeErrorMessage($userLang);

        // For debugging, add the actual error message (in production, you might want to remove this)
        $errorMessage .= " (Error: " . $e->getMessage() . ")";

        echo json_encode(new ErrorResult($errorMessage, 500));
    }
}
