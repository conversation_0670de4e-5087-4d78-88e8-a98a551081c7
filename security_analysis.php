<?php
/**
 * Security Analysis Endpoints
 * This file provides endpoints for analyzing login security and detecting suspicious activities
 */

require_once('config.php');
require_once('models/ResultModel.php');
require_once('login_tracking.php');
require_once('utils.php');

// Apply CORS headers
cors_client();

// Get the function parameter
$f = $_POST['f'] ?? $_GET['f'] ?? '';

switch ($f) {
    case 'get_failed_login_stats':
        get_failed_login_stats();
        break;
    case 'get_suspicious_ips':
        get_suspicious_ips();
        break;
    case 'get_recent_failed_attempts':
        get_recent_failed_attempts();
        break;
    default:
        $response = new ErrorResult('Invalid function parameter.');
        $response->send(400);
        break;
}

/**
 * Get failed login statistics
 */
function get_failed_login_stats() {
    // Validate JWT token and get user ID
    $userId = validate_jwt();
    if (!$userId) {
        $response = new ErrorResult('Unauthorized access.');
        $response->send(401);
        return;
    }

    // Check if user has admin permissions (you may want to implement this)
    // For now, we'll allow any authenticated user to see general stats
    
    $hours = $_POST['hours'] ?? $_GET['hours'] ?? 24;
    $hours = max(1, min(168, intval($hours))); // Limit between 1 hour and 1 week
    
    $stats = getFailedLoginStats($hours);
    
    $response = new SuccessResult($stats);
    $response->send();
}

/**
 * Get list of suspicious IP addresses
 */
function get_suspicious_ips() {
    // Validate JWT token and get user ID
    $userId = validate_jwt();
    if (!$userId) {
        $response = new ErrorResult('Unauthorized access.');
        $response->send(401);
        return;
    }

    global $link;
    
    $hours = $_POST['hours'] ?? $_GET['hours'] ?? 24;
    $hours = max(1, min(168, intval($hours))); // Limit between 1 hour and 1 week
    $threshold = $_POST['threshold'] ?? $_GET['threshold'] ?? 5; // Minimum failed attempts to be considered suspicious
    
    $query = "SELECT 
                ip_address,
                COUNT(*) as failed_attempts,
                COUNT(DISTINCT attempted_email) as unique_emails_tried,
                MIN(created_at) as first_attempt,
                MAX(created_at) as last_attempt,
                GROUP_CONCAT(DISTINCT attempted_email ORDER BY created_at DESC LIMIT 5) as sample_emails
              FROM user_login_history 
              WHERE success = 0 
              AND created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
              AND ip_address IS NOT NULL
              GROUP BY ip_address 
              HAVING failed_attempts >= ?
              ORDER BY failed_attempts DESC 
              LIMIT 50";
    
    $stmt = mysqli_prepare($link, $query);
    if (!$stmt) {
        $response = new ErrorResult('Database error.');
        $response->send(500);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "ii", $hours, $threshold);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $suspicious_ips = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $suspicious_ips[] = $row;
    }
    
    $response = new SuccessResult([
        'suspicious_ips' => $suspicious_ips,
        'analysis_period_hours' => $hours,
        'threshold' => $threshold
    ]);
    $response->send();
}

/**
 * Get recent failed login attempts
 */
function get_recent_failed_attempts() {
    // Validate JWT token and get user ID
    $userId = validate_jwt();
    if (!$userId) {
        $response = new ErrorResult('Unauthorized access.');
        $response->send(401);
        return;
    }

    global $link;
    
    $limit = $_POST['limit'] ?? $_GET['limit'] ?? 50;
    $limit = max(1, min(500, intval($limit))); // Limit between 1 and 500
    
    $query = "SELECT 
                id,
                user_id,
                login_type,
                ip_address,
                attempted_email,
                failure_reason,
                device_type,
                browser,
                os,
                created_at
              FROM user_login_history 
              WHERE success = 0 
              ORDER BY created_at DESC 
              LIMIT ?";
    
    $stmt = mysqli_prepare($link, $query);
    if (!$stmt) {
        $response = new ErrorResult('Database error.');
        $response->send(500);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "i", $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $failed_attempts = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Mask email for privacy (show only first 2 chars and domain)
        if ($row['attempted_email']) {
            $email_parts = explode('@', $row['attempted_email']);
            if (count($email_parts) == 2) {
                $row['attempted_email'] = substr($email_parts[0], 0, 2) . '***@' . $email_parts[1];
            }
        }
        $failed_attempts[] = $row;
    }
    
    $response = new SuccessResult([
        'failed_attempts' => $failed_attempts,
        'limit' => $limit
    ]);
    $response->send();
}
?>
