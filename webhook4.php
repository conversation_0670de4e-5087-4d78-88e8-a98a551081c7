<?php

ini_set('memory_limit', '-1');
set_time_limit(0);

include_once('config.php');

$webhookUrl = "https://hook.eu2.make.com/gvr17qdbeszk4f46jaqi5ezyv1b9xf0m";


global $link;




$our_data = array();
$obj2 = array();

$rs = mysqli_query($link, "SELECT coindata.*, coindata2.links from coindata
left join coindata2 on coindata.cr_id = coindata2.cr_id
order by id asc limit 100;");

while ($obj2 = mysqli_fetch_assoc($rs)) {




    $our_data[] = $obj2;

    $data['coin_name'] = $obj2['name'];
    $data['links'] = $obj2['links'];

    // Initialize cURL
$ch = curl_init($webhookUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
]);

// Execute cURL and close
$response = curl_exec($ch);

//echo $response;

curl_close($ch);



sleep(6);


}





/*
// Sample data payload
$data = [
    'coin_name' => 'SOLANA',
    'links' => '[{"type":"web","value":"https:\/\/injective.com\/"},{"type":"medium","value":"https:\/\/blog.injective.com\/"},{"type":"reddit","value":"https:\/\/www.reddit.com\/r\/injective\/"},{"type":"telegram","value":"http:\/\/t.me\/joininjective"},{"type":"explorer","value":"https:\/\/explorer.injective.network\/"},{"type":"facebook","value":"https:\/\/www.facebook.com\/injectiveprotocol"},{"type":"whitepaper","value":"https:\/\/docs.injective.network\/"},{"type":"twitter","value":"https:\/\/twitter.com\/injective"},{"type":"github","value":"https:\/\/github.com\/InjectiveLabs\/"},{"type":"discord","value":"https:\/\/discord.gg\/injective"}] '

];


$ch = curl_init($webhookUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
]);

// Execute cURL and close
$response = curl_exec($ch);

//echo $response;

curl_close($ch);

*/



?>