<?php
require 'vendor/autoload.php';
require_once('generate_client_secret.php');
// Hata raporlamayı aktifleştir
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);
function getAppleAccessToken($code)
{
    $client_id = "com.zdc.eviotlogin";
    $client_secret = generateAppleClientSecret();
    $redirect_uri = "https://api.coinscout.app/apple/callback.php";
    $post_data = [
        'client_id' => $client_id,
        'client_secret' => $client_secret,
        'code' => $code,
        'grant_type' => 'authorization_code',
        'redirect_uri' => $redirect_uri
    ];
    // CURL isteğini detaylı loglama ile yapılandır
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://appleid.apple.com/auth/token");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    $response = curl_exec($ch);
    // CURL hata kontrolü
    if (curl_errno($ch)) {
        error_log("CURL Error: " . curl_error($ch));
    }
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    error_log("HTTP Response Code: " . $http_code);
    curl_close($ch);
    return json_decode($response, true);
}
// Gelen parametreleri logla
error_log("Received GET parameters: " . print_r($_GET, true));
error_log("Received POST parameters: " . print_r($_POST, true));
if (isset($_POST['code'])) {
    $code = $_POST['code'];
    $user = isset($_POST['user']) ? $_POST['user'] : null;
    error_log("Received authorization code: " . $code);
    $token_data = getAppleAccessToken($code);
    error_log("Token response: " . print_r($token_data, true));
    if (isset($token_data['id_token'])) {
        // Başarılı login işlemi
        echo json_encode([
            'status' => 'success',
            'message' => 'Apple Login Successful',
            'data' => $token_data,
            'user' => $user ?? null
        ]);
    } else {
        // Token alınamadı
        echo json_encode([
            'status' => 'error',
            'message' => 'Failed to get token',
            'error' => $token_data
        ]);
    }
} else {
    // Code parametresi bulunamadı
    echo json_encode([
        'status' => 'error',
        'message' => 'No authorization code found'
    ]);
}
