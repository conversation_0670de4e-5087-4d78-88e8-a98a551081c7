<?php
require 'vendor/autoload.php';
use Firebase\JWT\JWT;
function generateAppleClientSecret() {
    $team_id = "D2CM52396J"; // Apple Developer Team ID
    $client_id = "com.zdc.eviotlogin"; // Service ID
    $key_id = "P586LDH5TL"; // Apple Developer Console'da oluşturduğunuz Key ID
    $private_key_path = __DIR__ . "/AuthKey_P586LDH5TL.p8";// İndirdiğiniz Private Key dosyasının adı
    $aud = "https://appleid.apple.com";
    // JWT payload
    $payload = [
        "iss" => $team_id,
        "iat" => time(),
        "exp" => time() + 3600, // 1 saat geçerli
        "aud" => $aud,
        "sub" => $client_id
    ];
    $private_key = file_get_contents($private_key_path);
    $private_key_resource = openssl_pkey_get_private($private_key);
    if (!$private_key_resource) {
        die("Hata: OpenSSL private key hatası.");
    }
    return JWT::encode($payload, $private_key_resource, 'ES256', $key_id);
}
// JWT oluştur ve ekrana yazdır
//echo generateAppleClientSecret();
?>
