# Login Tracking System

Bu sistem kullanıcı login aktivitelerini takip etmek ve analiz etmek için geliştirilmiştir.

## Özellikler

### 1. Last Login Tracking
- `users` tablosuna `last_login_at` al<PERSON><PERSON>
- Her başarılı login'de bu alan <PERSON>

### 2. Detayl<PERSON> Login Geçmişi
- `user_login_history` tablosu oluşturuldu
- Her login denemesi (başarılı/başarısız) kaydedilir
- IP adresi, user agent, cihaz bilgileri saklanır

### 3. <PERSON><PERSON> Türleri
- `email`: Email/şifre ile login
- `google`: Google OAuth ile login
- `apple`: Apple OAuth ile login
- `twitter`: Twitter OAuth ile login
- `refresh_token`: Refresh token ile yenileme

## Veritabanı Değişiklikleri

### 1. Users Tablosu
```sql
ALTER TABLE users
ADD COLUMN last_login_at TIMESTAMP NULL AFTER created_at;
```

### 2. User Login History Tablosu
```sql
CREATE TABLE IF NOT EXISTS user_login_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL COMMENT 'User ID - NULL for failed attempts where user was not found',
    login_type ENUM('email', 'google', 'apple', 'twitter', 'refresh_token') NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    country VARCHAR(100) NULL,
    city VARCHAR(100) NULL,
    device_type VARCHAR(50) NULL,
    browser VARCHAR(100) NULL,
    os VARCHAR(100) NULL,
    success TINYINT(1) NOT NULL DEFAULT 1,
    failure_reason VARCHAR(255) NULL,
    attempted_email VARCHAR(255) NULL COMMENT 'Email used in failed login attempts (for security analysis)',
    session_duration INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Indexes
    INDEX (user_id),
    INDEX (login_type),
    INDEX (ip_address),
    INDEX (created_at),
    INDEX (success),
    INDEX (attempted_email),
    INDEX (ip_success_date) (ip_address, success, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

**Önemli Değişiklikler:**
- `user_id` artık `NULL` değerlere izin veriyor (başarısız login denemelerinde kullanıcı bulunamadığında)
- `attempted_email` alanı eklendi (güvenlik analizi için)
- Foreign key constraint kaldırıldı (NULL user_id değerleri için)
- Yeni indexler eklendi (performans için)

## Güvenlik Özellikleri

### Otomatik Şüpheli Aktivite Tespiti
- **IP Bazlı Analiz**: Aynı IP'den 1 saat içinde 10+ başarısız deneme
- **Email Enumeration Tespiti**: Aynı email ile 1 saat içinde 5+ başarısız deneme
- **Gerçek Zamanlı Uyarılar**: StripeLogger ile otomatik email bildirimleri

### Güvenlik Analizi Endpoints
- `security_analysis.php?f=get_failed_login_stats` - Başarısız login istatistikleri
- `security_analysis.php?f=get_suspicious_ips` - Şüpheli IP adresleri
- `security_analysis.php?f=get_recent_failed_attempts` - Son başarısız denemeler

### Veri Koruma
- Email adresleri maskelenerek gösterilir (örn: `th***@yahoo.com`)
- Sadece yetkili kullanıcılar güvenlik verilerine erişebilir
- Hassas veriler loglanmaz

## Kurulum

### 1. Mevcut Tabloyu Güncelleyin
Eğer `user_login_history` tablosu zaten varsa:
```bash
mysql -u username -p database_name < fix_login_tracking_table.sql
```

### 2. Yeni Kurulum İçin SQL Scriptlerini Çalıştırın
```bash
# Veritabanı değişikliklerini uygula
mysql -u username -p database_name < add_login_tracking.sql
```

### 2. PHP Dosyalarını Dahil Edin
- `login_tracking.php` - Ana tracking fonksiyonları
- `login_history_api.php` - API endpoints
- `authentication.php` - Güncellenmiş authentication

## API Kullanımı

### Login Geçmişini Getir
```javascript
POST /login_history_api.php
{
    "f": "get_login_history",
    "limit": 20,
    "days": 30
}
```

### Login İstatistiklerini Getir
```javascript
POST /login_history_api.php
{
    "f": "get_login_stats",
    "days": 30
}
```

### Son Başarılı Login'leri Getir
```javascript
POST /login_history_api.php
{
    "f": "get_recent_logins",
    "limit": 10
}
```

### Başarısız Login Denemelerini Getir
```javascript
POST /login_history_api.php
{
    "f": "get_failed_attempts",
    "days": 7
}
```

## Fonksiyonlar

### handleSuccessfulLogin($userId, $loginType, $additionalData = [])
Başarılı login'i kaydeder:
- `last_login_at` alanını günceller
- Login geçmişine kayıt ekler
- User agent'ı parse eder

### handleFailedLogin($userId, $loginType, $failureReason, $additionalData = [])
Başarısız login denemesini kaydeder:
- Login geçmişine hata kaydı ekler
- Güvenlik analizi için kullanılabilir

### getUserLoginHistory($userId, $limit = 10, $days = 30)
Kullanıcının login geçmişini getirir

### parseUserAgent($userAgent)
User agent string'ini parse eder:
- Device type (mobile, desktop, tablet)
- Browser (Chrome, Firefox, Safari, etc.)
- OS (Windows, macOS, iOS, Android, etc.)

## Güvenlik Özellikleri

### 1. Şüpheli Aktivite Tespiti
- Aynı IP'den çok fazla başarısız deneme
- Farklı lokasyonlardan eş zamanlı login
- Bilinmeyen cihazlardan erişim

### 2. Analitik Views
```sql
-- Son 30 gündeki başarılı login'ler
CREATE VIEW recent_successful_logins AS ...

-- Başarısız login denemeleri
CREATE VIEW failed_login_attempts AS ...
```

### 3. Performans İndeksleri
- `user_id` + `created_at` composite index
- `ip_address` + `created_at` composite index
- `success` + `created_at` composite index

## Örnek Kullanım Senaryoları

### 1. Kullanıcı Dashboard'ında Son Login Bilgisi
```php
$lastLogin = $user['last_login_at'];
echo "Son giriş: " . date('d.m.Y H:i', strtotime($lastLogin));
```

### 2. Güvenlik Uyarıları
```php
$recentLogins = getUserLoginHistory($userId, 5, 1);
foreach ($recentLogins as $login) {
    if ($login['ip_address'] !== $currentIP) {
        // Farklı IP'den giriş uyarısı
    }
}
```

### 3. Login İstatistikleri
```php
// Son 30 gündeki login sayısı
$stats = getLoginStats($userId, 30);
echo "Bu ay {$stats['successful_logins']} kez giriş yaptınız";
```

## Veri Saklama Politikası

### Önerilen Saklama Süreleri
- **Başarılı login'ler**: 90 gün
- **Başarısız denemeler**: 30 gün
- **Güvenlik olayları**: 1 yıl

### Temizlik Script'i
```sql
-- 90 günden eski başarılı login kayıtlarını sil
DELETE FROM user_login_history
WHERE success = 1
AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 30 günden eski başarısız deneme kayıtlarını sil
DELETE FROM user_login_history
WHERE success = 0
AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

## GDPR Uyumluluğu

### Kullanıcı Hakları
- Kullanıcılar kendi login geçmişlerini görebilir
- Veri silme talebi durumunda tüm kayıtlar silinir
- IP adresleri anonim hale getirilebilir

### Veri Minimizasyonu
- Sadece gerekli bilgiler saklanır
- Hassas veriler şifrelenir
- Düzenli veri temizliği yapılır

## Monitoring ve Alerting

### Önemli Metrikler
- Günlük başarısız login sayısı
- Aynı IP'den çoklu başarısız denemeler
- Yeni cihazlardan login'ler
- Coğrafi anomaliler

### Alert Koşulları
```sql
-- Aynı IP'den 1 saatte 5+ başarısız deneme
SELECT ip_address, COUNT(*) as attempts
FROM user_login_history
WHERE success = 0
AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY ip_address
HAVING attempts >= 5;
```

## Performans Optimizasyonu

### İndeks Stratejisi
- Composite indexler sık kullanılan sorgular için
- Partitioning büyük tablolar için
- Archive tabloları eski veriler için

### Caching
- Son login bilgileri Redis'te cache'lenebilir
- Sık erişilen istatistikler cache'lenebilir

## Gelecek Geliştirmeler

### 1. GeoIP Entegrasyonu
- IP adreslerinden konum tespiti
- Şüpheli lokasyon uyarıları

### 2. Device Fingerprinting
- Cihaz parmak izi oluşturma
- Bilinmeyen cihaz tespiti

### 3. Machine Learning
- Anormal davranış tespiti
- Risk skorlama sistemi

### 4. Real-time Notifications
- WebSocket ile anlık uyarılar
- Email/SMS güvenlik bildirimleri
