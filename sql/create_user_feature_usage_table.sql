-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ın özellik kullanım sayılarını tutacak tablo
-- Free kullanıcılar için compare_coins gibi sınırlı özelliklerin kullanım sayısını takip eder

CREATE TABLE user_feature_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    feature_name VARCHAR(50) NOT NULL,
    usage_date DATE NOT NULL,
    usage_count INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint: Her kullanıcı için her özellik için günde bir kayıt
    UNIQUE KEY unique_user_feature_date (user_id, feature_name, usage_date),
    
    -- Index'ler performans için
    INDEX idx_user_feature (user_id, feature_name),
    INDEX idx_usage_date (usage_date),
    INDEX idx_user_feature_date (user_id, feature_name, usage_date)
);

-- Örnek kullanım:
-- INSERT INTO user_feature_usage (user_id, feature_name, usage_date, usage_count) 
-- VALUES (123, 'compare_coins', CURDATE(), 1)
-- ON DUPLICATE KEY UPDATE usage_count = usage_count + 1;

-- Son 30 günde kullanım sayısını sorgulama:
-- SELECT SUM(usage_count) as total_usage 
-- FROM user_feature_usage 
-- WHERE user_id = 123 
--   AND feature_name = 'compare_coins' 
--   AND usage_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY);
