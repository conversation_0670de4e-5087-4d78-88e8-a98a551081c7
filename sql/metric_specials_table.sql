-- <PERSON><PERSON> script to create the metric_specials table
-- This table stores special metric data for specific coins and metrics

CREATE TABLE IF NOT EXISTS metric_specials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_id INT NOT NULL,
    coin_id INT NOT NULL,
    `key` VARCHAR(255) NOT NULL,
    `value` TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX (metric_id),
    INDEX (coin_id),
    INDEX (metric_id, coin_id),
    INDEX (`key`),

    UNIQUE KEY unique_metric_coin_key (metric_id, coin_id, `key`),

    CONSTRAINT fk_metric FOREIGN KEY (metric_id) REFERENCES metric_subgroups(id) ON DELETE CASCADE,
    CONSTRAINT fk_coin FOREIGN KEY (coin_id) REFERENCES coindata(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- Sample data for testing different metrics
-- Uncomment the lines below to insert test data

-- Metric ID 2: Supply information (Tokenomics)
-- INSERT INTO metric_specials (metric_id, coin_id, `key`, `value`) VALUES
-- (2, 'bitcoin', 'max_supply', '21000000'),
-- (2, 'bitcoin', 'total_supply', '19750000'),
-- (2, 'bitcoin', 'circulating_supply', '19750000'),
-- (2, 'ethereum', 'max_supply', NULL),
-- (2, 'ethereum', 'total_supply', '120280000'),
-- (2, 'ethereum', 'circulating_supply', '120280000'),

-- Metric ID 3: Security information
-- (3, 'bitcoin', 'audit_score', '95'),
-- (3, 'bitcoin', 'security_rating', 'A+'),
-- (3, 'bitcoin', 'vulnerability_count', '0'),
-- (3, 'ethereum', 'audit_score', '88'),
-- (3, 'ethereum', 'security_rating', 'A'),
-- (3, 'ethereum', 'vulnerability_count', '2'),

-- Metric ID 4: Social metrics
-- (4, 'bitcoin', 'twitter_followers', '5200000'),
-- (4, 'bitcoin', 'telegram_members', '0'),
-- (4, 'bitcoin', 'reddit_subscribers', '4800000'),
-- (4, 'ethereum', 'twitter_followers', '3100000'),
-- (4, 'ethereum', 'telegram_members', '0'),
-- (4, 'ethereum', 'reddit_subscribers', '1200000'),

-- Metric ID 5: Market metrics
-- (5, 'bitcoin', 'market_cap', '850000000000'),
-- (5, 'bitcoin', 'volume_24h', '25000000000'),
-- (5, 'bitcoin', 'price_volatility', '3.2'),
-- (5, 'ethereum', 'market_cap', '280000000000'),
-- (5, 'ethereum', 'volume_24h', '12000000000'),
-- (5, 'ethereum', 'price_volatility', '4.1'),

-- Metric ID 6: Fundamentals
-- (6, 'bitcoin', 'team_score', '90'),
-- (6, 'bitcoin', 'roadmap_completion', '85'),
-- (6, 'bitcoin', 'partnership_count', '150'),
-- (6, 'ethereum', 'team_score', '95'),
-- (6, 'ethereum', 'roadmap_completion', '78'),
-- (6, 'ethereum', 'partnership_count', '300');
