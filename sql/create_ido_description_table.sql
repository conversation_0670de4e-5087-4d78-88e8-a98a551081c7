-- SQL script to create the ido_description table with multilingual support
-- This table stores IDO descriptions and summaries in multiple languages

CREATE TABLE ido_description (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cr_key VARCHAR(255) NOT NULL UNIQUE,
    
    -- Arabic
    description_AR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_AR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- German
    description_DE TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_DE TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- English
    description_EN TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_EN TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Spanish
    description_ES TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_ES TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- French
    description_FR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_FR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Hindi
    description_HI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_HI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Indonesian
    description_ID TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_ID TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Italian
    description_IT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_IT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Japanese
    description_JA TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_JA TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Korean
    description_KO TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_KO TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Portuguese
    description_PT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_PT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Russian
    description_RU TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_RU TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Turkish
    description_TR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_TR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Vietnamese
    description_VI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_VI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Chinese
    description_ZH TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    summary_ZH TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX (cr_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add comments to explain the table structure
-- ido_description:
-- id: Primary key
-- cr_key: The cr_key used to match with client_ico_coin_list table
-- description_XX: Full description text for each language (XX = language code in uppercase)
-- summary_XX: Summary/short description text for each language (XX = language code in uppercase)
-- created_at: When the record was created
-- updated_at: When the record was last updated

-- Supported languages (from language_config.php):
-- AR (Arabic), DE (German), EN (English), ES (Spanish), FR (French)
-- HI (Hindi), ID (Indonesian), IT (Italian), JA (Japanese), KO (Korean)
-- PT (Portuguese), RU (Russian), TR (Turkish), VI (Vietnamese), ZH (Chinese)
