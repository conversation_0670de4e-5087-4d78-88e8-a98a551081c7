
-- Stripe Tables SQL
-- This file contains all the SQL statements needed to create the tables for Stripe integration

-- Create stripe_products table
CREATE TABLE IF NOT EXISTS stripe_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stripe_product_id VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT 1,
    metadata JSON,
    images JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create stripe_prices table
CREATE TABLE IF NOT EXISTS stripe_prices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stripe_price_id VARCHAR(255) NOT NULL UNIQUE,
    stripe_product_id VARCHAR(255) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    unit_amount DECIMAL(10, 2) NOT NULL,
    `interval` VARCHAR(20),
    interval_count INT,
    active BOOLEAN DEFAULT 1,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (stripe_product_id) REFERENCES stripe_products(stripe_product_id) ON DELETE CASCADE
);

-- Create stripe_checkout_sessions table
CREATE TABLE IF NOT EXISTS stripe_checkout_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    checkout_session_id VARCHAR(255) NOT NULL UNIQUE,
    client_reference_id VARCHAR(255),
    customer_id VARCHAR(255),
    subscription_id VARCHAR(255),
    email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (customer_id),
    INDEX (subscription_id)
);

-- Create stripe_user_subscriptions table
CREATE TABLE IF NOT EXISTS stripe_user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    stripe_subscription_id VARCHAR(255) NOT NULL UNIQUE,
    customer_email VARCHAR(255),
    plan_id VARCHAR(255),
    plan_name VARCHAR(255),
    plan_amount DECIMAL(10, 2),
    `plan_interval` VARCHAR(20),
    status VARCHAR(50) NOT NULL,
    stripe_price_id VARCHAR(255),
    current_period_end DATETIME,
    cancel_at_period_end BOOLEAN DEFAULT 0,
    canceled_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (user_id),
    INDEX (stripe_customer_id),
    INDEX (status)
);

-- Create stripe_payment_history table
CREATE TABLE IF NOT EXISTS stripe_payment_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    stripe_payment_intent_id VARCHAR(255),
    stripe_invoice_id VARCHAR(255),
    stripe_subscription_id VARCHAR(255),
    payment_method_id VARCHAR(255),
    amount DECIMAL(10, 2) NOT NULL,
    amount_paid DECIMAL(10, 2),
    amount_due DECIMAL(10, 2),
    currency VARCHAR(10) NOT NULL,
    status VARCHAR(50) NOT NULL,
    error_message TEXT,
    error_code VARCHAR(100),
    period_start DATETIME,
    period_end DATETIME,
    attempt_count INT,
    next_payment_attempt DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (user_id),
    INDEX (stripe_customer_id),
    INDEX (stripe_payment_intent_id),
    INDEX (stripe_invoice_id),
    INDEX (stripe_subscription_id),
    INDEX (status)
);

-- Create stripe_subscription_changes table to track subscription upgrades/downgrades
CREATE TABLE IF NOT EXISTS stripe_subscription_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    stripe_subscription_id VARCHAR(255) NOT NULL,
    change_type VARCHAR(50) NOT NULL,
    from_level VARCHAR(50) NOT NULL,
    to_level VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX (user_id),
    INDEX (stripe_subscription_id)
);

-- Create stripe_coupons table
CREATE TABLE IF NOT EXISTS stripe_coupons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stripe_coupon_id VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255),
    percent_off DECIMAL(5, 2) NULL,
    amount_off DECIMAL(10, 2) NULL,
    currency VARCHAR(10),
    duration VARCHAR(50),
    duration_in_months INT NULL,
    max_redemptions INT NULL,
    times_redeemed INT DEFAULT 0,
    valid_until DATETIME NULL,
    is_active BOOLEAN DEFAULT 1,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create stripe_promotion_codes table
CREATE TABLE IF NOT EXISTS stripe_promotion_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stripe_promotion_code_id VARCHAR(255) NOT NULL UNIQUE,
    stripe_coupon_id VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    active BOOLEAN DEFAULT 1,
    expires_at DATETIME NULL,
    max_redemptions INT NULL,
    times_redeemed INT DEFAULT 0,
    first_time_transaction BOOLEAN DEFAULT 0,
    minimum_amount DECIMAL(10, 2) NULL,
    minimum_amount_currency VARCHAR(10),
    customer_specific BOOLEAN DEFAULT 0,
    customer_id VARCHAR(255) NULL,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (stripe_coupon_id),
    INDEX (code),
    INDEX (customer_id)
);

-- Create stripe_customer_discounts table
CREATE TABLE IF NOT EXISTS stripe_customer_discounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    stripe_subscription_id VARCHAR(255) NULL,
    stripe_coupon_id VARCHAR(255) NOT NULL,
    percent_off DECIMAL(5, 2) NULL,
    amount_off DECIMAL(10, 2) NULL,
    currency VARCHAR(10),
    start_date DATETIME NOT NULL,
    end_date DATETIME NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (user_id),
    INDEX (stripe_customer_id),
    INDEX (stripe_subscription_id),
    INDEX (stripe_coupon_id)
);

-- Optional foreign key constraints (commented out by default)
-- Uncomment these if you want to add foreign key constraints

-- ALTER TABLE stripe_user_subscriptions
--     ADD CONSTRAINT fk_stripe_user_subscriptions_user_id
--     FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- ALTER TABLE stripe_payment_history
--     ADD CONSTRAINT fk_stripe_payment_history_user_id
--     FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- ALTER TABLE stripe_payment_history
--     ADD CONSTRAINT fk_stripe_payment_history_subscription_id
--     FOREIGN KEY (stripe_subscription_id) REFERENCES stripe_user_subscriptions(stripe_subscription_id) ON DELETE SET NULL;

-- ALTER TABLE stripe_subscription_changes
--     ADD CONSTRAINT fk_stripe_subscription_changes_user_id
--     FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- ALTER TABLE stripe_subscription_changes
--     ADD CONSTRAINT fk_stripe_subscription_changes_subscription_id
--     FOREIGN KEY (stripe_subscription_id) REFERENCES stripe_user_subscriptions(stripe_subscription_id) ON DELETE CASCADE;

