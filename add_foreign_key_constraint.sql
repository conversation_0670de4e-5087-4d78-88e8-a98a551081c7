-- Bu SQL betiği, password_resets tablosuna foreign key constraint ekler
-- Bu sayede bir kullan<PERSON><PERSON><PERSON> silind<PERSON>, ilgili password_resets kayıtları da otomatik olarak silinir

-- Önce mevcut password_resets tablosunun yapısını kontrol edelim
-- <PERSON>ğer user_id sütunu yoksa ekleyelim
ALTER TABLE password_resets ADD COLUMN IF NOT EXISTS user_id INT;

-- <PERSON><PERSON><PERSON> email sütunu varsa, bu sütundaki değerlere göre user_id sütununu dolduralım
UPDATE password_resets pr
JOIN users u ON pr.email = u.email
SET pr.user_id = u.id
WHERE pr.user_id IS NULL;

-- <PERSON><PERSON><PERSON> bir index yoksa, user_id sütunu için index ekleyelim
ALTER TABLE password_resets ADD INDEX IF NOT EXISTS idx_user_id (user_id);

-- Foreign key constraint ekleyelim
-- NOT: Eğer constraint zaten varsa, önce onu silmemiz gerekebilir
-- ALTER TABLE password_resets DROP FOREIGN KEY IF EXISTS fk_password_resets_user_id;

ALTER TABLE password_resets
ADD CONSTRAINT fk_password_resets_user_id
FOREIGN KEY (user_id)
REFERENCES users(id)
ON DELETE CASCADE;

-- Aynı şekilde, user_subscriptions tablosuna da foreign key constraint ekleyelim
ALTER TABLE user_subscriptions
ADD CONSTRAINT fk_user_subscriptions_user_id
FOREIGN KEY (user_id)
REFERENCES users(id)
ON DELETE CASCADE;

-- Aynı şekilde, watchlists tablosuna da foreign key constraint ekleyelim
ALTER TABLE watchlists
ADD CONSTRAINT fk_watchlists_user_id
FOREIGN KEY (user_id)
REFERENCES users(id)
ON DELETE CASCADE;

-- Aynı şekilde, ido_watchlists tablosuna da foreign key constraint ekleyelim
ALTER TABLE ido_watchlists
ADD CONSTRAINT fk_ido_watchlists_user_id
FOREIGN KEY (user_id)
REFERENCES users(id)
ON DELETE CASCADE;

-- Aynı şekilde, portfolio tablosuna da foreign key constraint ekleyelim
ALTER TABLE portfolio
ADD CONSTRAINT fk_portfolio_user_id
FOREIGN KEY (user_id)
REFERENCES users(id)
ON DELETE CASCADE;

-- Aynı şekilde, user_alerts tablosuna da foreign key constraint ekleyelim
ALTER TABLE user_alerts
ADD CONSTRAINT fk_user_alerts_user_id
FOREIGN KEY (user_id)
REFERENCES users(id)
ON DELETE CASCADE;

-- Aynı şekilde, user_notifications tablosuna da foreign key constraint ekleyelim
ALTER TABLE user_notifications
ADD CONSTRAINT fk_user_notifications_user_id
FOREIGN KEY (user_id)
REFERENCES users(id)
ON DELETE CASCADE;

-- Aynı şekilde, payment_history tablosuna da foreign key constraint ekleyelim
ALTER TABLE payment_history
ADD CONSTRAINT fk_payment_history_user_id
FOREIGN KEY (user_id)
REFERENCES users(id)
ON DELETE CASCADE;

-- Aynı şekilde, checkout_sessions tablosuna da foreign key constraint ekleyelim
-- NOT: Bu tabloda client_reference_id sütunu varsa ve bu users.id'ye referans veriyorsa
ALTER TABLE checkout_sessions
ADD COLUMN IF NOT EXISTS user_id INT;

-- client_reference_id değerlerini user_id sütununa kopyalayalım
UPDATE checkout_sessions
SET user_id = CAST(client_reference_id AS UNSIGNED)
WHERE user_id IS NULL AND client_reference_id REGEXP '^[0-9]+$';

-- Foreign key constraint ekleyelim
ALTER TABLE checkout_sessions
ADD CONSTRAINT fk_checkout_sessions_user_id
FOREIGN KEY (user_id)
REFERENCES users(id)
ON DELETE CASCADE;
