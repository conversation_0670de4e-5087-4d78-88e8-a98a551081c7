<?php

/**
 * Allowed Emails Whitelist (Database Version)
 *
 * This file contains functions to check email addresses against the allowed_emails table in the database.
 * This replaces the previous static array approach.
 */

/**
 * Get the list of allowed email addresses from database
 *
 * @return array Array of allowed email addresses
 */
function get_allowed_emails()
{
    global $link;

    $query = "SELECT email FROM allowed_emails";
    $result = mysqli_query($link, $query);

    $allowed_emails = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $allowed_emails[] = $row['email'];
        }
    }

    return $allowed_emails;
}

/**
 * Check if an email is in the whitelist (database version)
 *
 * @param string $email The email address to check
 * @return bool True if the email is allowed, false otherwise
 */
function is_email_allowed($email)
{
    global $link;

    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    // Convert email to lowercase for case-insensitive comparison
    $email = strtolower(trim($email));

    // Escape the email for safe database query
    $escaped_email = mysqli_real_escape_string($link, $email);

    // Query to check if email exists in allowed_emails table
    $query = "SELECT COUNT(*) as count FROM allowed_emails WHERE LOWER(email) = '$escaped_email'";
    $result = mysqli_query($link, $query);

    $is_allowed = false;
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $is_allowed = ($row['count'] > 0);
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "EMAIL WHITELIST - Checking email access from database", [
        'email' => $email,
        'is_allowed' => $is_allowed,
        'query_executed' => true
    ]);

    return $is_allowed;
}

/**
 * Add an email to the whitelist (optional helper function)
 *
 * @param string $email The email address to add
 * @return bool True if successfully added, false otherwise
 */
function add_allowed_email($email)
{
    global $link;

    $email = strtolower(trim($email));
    $escaped_email = mysqli_real_escape_string($link, $email);

    // Check if email already exists
    if (is_email_allowed($email)) {
        return true; // Already exists
    }

    $query = "INSERT INTO allowed_emails (email) VALUES ('$escaped_email')";
    $result = mysqli_query($link, $query);

    return $result !== false;
}

/**
 * Remove an email from the whitelist (optional helper function)
 *
 * @param string $email The email address to remove
 * @return bool True if successfully removed, false otherwise
 */
function remove_allowed_email($email)
{
    global $link;

    $email = strtolower(trim($email));
    $escaped_email = mysqli_real_escape_string($link, $email);

    $query = "DELETE FROM allowed_emails WHERE LOWER(email) = '$escaped_email'";
    $result = mysqli_query($link, $query);

    return $result !== false;
}