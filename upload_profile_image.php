<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/utils.php';
require_once __DIR__ . '/models/ResultModel.php';
require_once __DIR__ . '/clientmethods/ProfileImageMethods.php';

// Apply CORS headers
cors_client();

// Sadece POST isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    global $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $response = new ErrorResult($clientMessages[$lang]['method_not_allowed']);
    $response->send(405);
    exit;
}

// Kullanıcı kimlik doğrulaması
$userId = authenticate_user();
if (!$userId) {
    global $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $response = new ErrorResult($clientMessages[$lang]['unauthorized_access']);
    $response->send(401);
    exit;
}

// İşlem türünü belirle
$action = $_POST['action'] ?? 'upload';

switch ($action) {
    case 'upload':
        upload_profile_image();
        break;
    case 'delete':
        delete_profile_image();
        break;
    default:
        $response = new ErrorResult('Invalid action');
        $response->send(400);
        break;
}

?>
