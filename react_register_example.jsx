import React, { useState } from 'react';
import axios from 'axios';
import <PERSON>CAPTC<PERSON> from 'react-google-recaptcha';

const RegisterForm = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    fullName: '',
    country: '',
    termsAccepted: false
  });
  
  const [captchaToken, setCaptchaToken] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false
  });

  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
    
    // Check password strength
    if (name === 'password') {
      setPasswordStrength({
        length: value.length >= 8,
        uppercase: /[A-Z]/.test(value),
        lowercase: /[a-z]/.test(value),
        number: /[0-9]/.test(value),
        special: /[^A-Za-z0-9]/.test(value)
      });
    }
  };

  // Handle CAPTCHA verification
  const handleCaptchaChange = (token) => {
    setCaptchaToken(token);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    // Validate form
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }
    
    if (!captchaToken) {
      setError('Please complete the CAPTCHA verification');
      setLoading(false);
      return;
    }
    
    if (!formData.termsAccepted) {
      setError('You must accept the terms and conditions');
      setLoading(false);
      return;
    }
    
    // Check password strength
    const { length, uppercase, lowercase, number, special } = passwordStrength;
    if (!(length && uppercase && lowercase && number && special)) {
      setError('Password does not meet complexity requirements');
      setLoading(false);
      return;
    }
    
    try {
      const response = await axios.post('https://api.coinscout.app/authentication.php', {
        f: "register_user",
        email: formData.email,
        password: formData.password,
        username: formData.username || undefined,
        full_name: formData.fullName || undefined,
        country: formData.country || undefined,
        terms_accepted: formData.termsAccepted,
        captcha_token: captchaToken
      });
      
      if (response.data.success) {
        setSuccess('Registration successful! Please check your email to verify your account.');
        // Reset form
        setFormData({
          email: '',
          password: '',
          confirmPassword: '',
          username: '',
          fullName: '',
          country: '',
          termsAccepted: false
        });
        setCaptchaToken(null);
      } else {
        setError(response.data.errormsg || 'Registration failed');
      }
    } catch (err) {
      setError(err.response?.data?.errormsg || 'An error occurred during registration');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="register-form">
      <h2>Create an Account</h2>
      
      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="email">Email Address *</label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="username">Username (optional)</label>
          <input
            type="text"
            id="username"
            name="username"
            value={formData.username}
            onChange={handleChange}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="fullName">Full Name (optional)</label>
          <input
            type="text"
            id="fullName"
            name="fullName"
            value={formData.fullName}
            onChange={handleChange}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="country">Country (optional)</label>
          <input
            type="text"
            id="country"
            name="country"
            value={formData.country}
            onChange={handleChange}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="password">Password *</label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            required
          />
          
          <div className="password-strength">
            <div className={passwordStrength.length ? 'valid' : 'invalid'}>
              At least 8 characters
            </div>
            <div className={passwordStrength.uppercase ? 'valid' : 'invalid'}>
              At least one uppercase letter
            </div>
            <div className={passwordStrength.lowercase ? 'valid' : 'invalid'}>
              At least one lowercase letter
            </div>
            <div className={passwordStrength.number ? 'valid' : 'invalid'}>
              At least one number
            </div>
            <div className={passwordStrength.special ? 'valid' : 'invalid'}>
              At least one special character
            </div>
          </div>
        </div>
        
        <div className="form-group">
          <label htmlFor="confirmPassword">Confirm Password *</label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            required
          />
        </div>
        
        <div className="form-group">
          <div className="checkbox-container">
            <input
              type="checkbox"
              id="termsAccepted"
              name="termsAccepted"
              checked={formData.termsAccepted}
              onChange={handleChange}
              required
            />
            <label htmlFor="termsAccepted">
              I accept the <a href="/terms" target="_blank">Terms and Conditions</a>
            </label>
          </div>
        </div>
        
        <div className="form-group">
          <ReCAPTCHA
            sitekey="YOUR_RECAPTCHA_SITE_KEY"
            onChange={handleCaptchaChange}
          />
        </div>
        
        <button type="submit" disabled={loading}>
          {loading ? 'Registering...' : 'Register'}
        </button>
      </form>
      
      <div className="login-link">
        Already have an account? <a href="/login">Log in</a>
      </div>
    </div>
  );
};

export default RegisterForm;
