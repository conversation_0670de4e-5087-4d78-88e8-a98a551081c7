<?php


cors2();
$is_debug = true;

//For security purposes, we can allow only specified agent type
$user_agent = $_SERVER['HTTP_USER_AGENT']; 
if (preg_match('/python/i', $user_agent)) { 
    
    
    echo 'You are forbidden!';

    foreach ($_POST as $key => $value) {
        echo "a";
        error_log("Python Script Istegi : ". $key."=".$value,0);
     
    }

    header('HTTP/1.0 403 Forbidden');

return;

 }




//$uri = $_SERVER['REQUEST_URI'];   // /api/users
//$method = $_SERVER['REQUEST_METHOD'];  // GET,POST,DELETE, etc.






include_once('config.php');


$payload = file_get_contents('php://input');


$data = json_decode($payload,true);

if($data == null)
{

    $debug_info = "";
    if($is_debug)
    $debug_info = "Payload is not a valid json or null";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;
}



$functName = "";

if (isset($data['f']) )
{
$functName = $data['f'];
}
else
{
    $debug_info = "";
    if($is_debug)
    $debug_info = "Endpoint variable is not set";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;  
}




$validEndpoints = array("get_gecko_coins","get_gecko_categories","get_gecko_single_coin_by_id","get_gecko_exchanges",
"update_gecko_exchange_extra_data", "calculate_scores_old",
"getInitData","getClientCampaignData","getactiveCampaignData");


if (in_array($functName, $validEndpoints)) {

    if ($functName == 'get_gecko_coins') {
        $functName();
    }
    else  if ($functName == 'get_gecko_categories') {
        $functName();
    }
    else  if ($functName == 'get_gecko_single_coin_by_id') {
        $functName();
    }
    else  if ($functName == 'get_gecko_exchanges') {
        $functName();
    }  
    else  if ($functName == 'update_gecko_exchange_extra_data') {
        $functName();
    }  
    else  if ($functName == 'calculate_scores_old') {
        $functName();
    }


    



    else if ($functName == 'getInitData') {
        $functName();
    }
    else if ($functName == 'getClientCampaignData') {
        $functName();
    }
    else if ($functName == 'getactiveCampaignData') {
        $functName();
    }

}
else
{
    $debug_info = "";
    if($is_debug)
    $debug_info = "Endpoint Not Exists";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
}




//---------------------------------------------------------------------//
//Admin Endpoints                                                      //
//---------------------------------------------------------------------//

function get_gecko_coins()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);
    
    global $data, $link, $gecko_api_key;



    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='gecko_coins'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'gecko_coins', now() )");
    (mysqli_stmt_execute($call));




    //First get our platform data to compare if data needs to be updated or insert

    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid from coindata3");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }


    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));


   





    //now get live data from CoinGecko

    $success = true;
    //$parameters = $data["parameters"];


    $curl = curl_init();

    $page = 1;


    $progressed_coin = 0;
    $changed_count = 0;
    $new_count = 0;



    $is_limit_reached = false;

    for($k = 1; $k < 1000; $k++)
    {

        if($is_limit_reached)
        {
            break;
        }


        $page = $k;



    curl_setopt_array($curl, array(
    CURLOPT_URL => 'https://pro-api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=id_asc&per_page=250&page='.$page,
   // CURLOPT_URL => 'https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=id_asc&per_page=250&page='.$page,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'GET',
    CURLOPT_HTTPHEADER => array(
        'Accept: application/json',
 //       'x-cg-demo-api-key: '.$gecko_api_key,
        'x-cg-pro-api-key: '.$gecko_api_key,
      ),
    ));

    $response = curl_exec($curl);

    $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if($httpcode != 200)
    {

        error_log("Response = ".$response,0);
        curl_close($curl);
        return;

    }



    $gecko_coins = json_decode($response, true);

    if(sizeof($gecko_coins) == 0)
    {
      
       break;
    }

    for($i =0; $i < sizeof($gecko_coins); $i++)
    {

        $progressed_coin++;


        $gecko_id = $gecko_coins[$i]['id'];
        $symbol = $gecko_coins[$i]['symbol'];
        $name = $gecko_coins[$i]['name'];
        $image = $gecko_coins[$i]['image'];
        $makretcap = $gecko_coins[$i]['market_cap'];
        $makretcap_rank = $gecko_coins[$i]['market_cap_rank'];
        $fdv = $gecko_coins[$i]['fully_diluted_valuation'];
        $total_volume = $gecko_coins[$i]['total_volume'];
        $total_supply = $gecko_coins[$i]['total_supply'];
        $max_supply = $gecko_coins[$i]['max_supply'];
        $circulating_supply = $gecko_coins[$i]['circulating_supply'];
        $ath = $gecko_coins[$i]['ath'];

        $image = isset($image) ? $image : NULL;
        $makretcap = isset($makretcap) ? $makretcap : NULL;
        $makretcap_rank = isset($makretcap_rank) ? $makretcap_rank : NULL;
        $fdv = isset($fdv) ? $fdv : NULL;
        $total_volume = isset($total_volume) ? $total_volume : NULL;
        $total_supply = isset($total_supply) ? $total_supply : NULL;
        $max_supply = !is_null($max_supply) ? $max_supply : NULL;
        $circulating_supply = isset($circulating_supply) ? $circulating_supply : NULL;
        $ath = isset($ath) ? $ath : NULL;
        


        /*
        if($makretcap != NULL && $makretcap < 100000)
        {
            $is_limit_reached = true;
            break;
        }
        */

        /*
        error_log("gecko_id=".$gecko_id,0);
        error_log("symbol=".$symbol,0);
        error_log("name=".$name,0);
        error_log("image=".$image,0);
        error_log("marketcap=".$makretcap,0);
        error_log("marketcap_rank=".$makretcap_rank,0);
        error_log("fdv=".$fdv,0);
        error_log("total_volume=".$total_volume,0);
        error_log("total_supply=".$total_supply,0);
        error_log("max_supply=".$max_supply,0);
        error_log("circulating_supply=".$circulating_supply,0);
        error_log("ath=".$ath,0);
        */
        

        $is_existing_coin = false;
        for($s=0; $s<sizeof($our_data); $s++)
        {
            if(strcmp($our_data[$s]["geckoid"], $gecko_id) == 0 )
            {
                $is_existing_coin = true;
                break;
            }
        }


        if($is_existing_coin)
        {

            $changed_count++;

            $call = mysqli_prepare($link, 'update coindata3 
            set symbol=?, name=?, image=?, marketcap=?, marketcap_rank=?,
            fdv=?, total_volume=?, total_supply=?, max_supply=?, circulating_supply=?, ath=?,
            update_date=now() where geckoid = ?');
      
            mysqli_stmt_bind_param($call, 'ssssissdddds', $symbol, $name, $image, $makretcap, $makretcap_rank, $fdv, $total_volume, $total_supply, $max_supply, $circulating_supply, $ath, $gecko_id   );
            $rs = (mysqli_stmt_execute($call));
    
            if(!$rs)
            {
            $success = false;
            error_log("gecko_id=".$gecko_id,0);
            error_log(mysqli_error($link),0); 
     
            }

        }
        else
        {

            $new_count++;

            $call = mysqli_prepare($link, 'insert into coindata3 
            (symbol, geckoid, name, image, marketcap, marketcap_rank, fdv, total_volume, total_supply, max_supply, circulating_supply, ath, create_date, update_date ) 
            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,  now(), now() )');
            mysqli_stmt_bind_param($call, 'ssssissdddds', $symbol, $gecko_id, $name, $image, $makretcap, $makretcap_rank, $fdv, $total_volume, $total_supply, $max_supply, $circulating_supply, $ath   );
            $rs = (mysqli_stmt_execute($call));
    
            if(!$rs)
            {
            $success = false;
            error_log("gecko_id=".$gecko_id,0);
            error_log(mysqli_error($link),0); 
     
            }


        }

      


        if (  ($progressed_coin+1)  %  (sizeof($our_data) / 100) == 0) {
            $progress = $progressed_coin /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }


        
    }


    sleep(2);

   }




    curl_close($curl);
   



    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" =>mysqli_error($link) );
        error_log( "Error details: " . mysqli_error($link) ,0);
    }


    
    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='gecko_coins'");
    (mysqli_stmt_execute($call));





    echo json_encode($resp, JSON_PRETTY_PRINT);






}



function get_gecko_categories()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $gecko_api_key;



      //First get our platform data to compare if data needs to be updated or insert

      $our_data = array();
      $obj2 = array();
      $rs = mysqli_query($link, "SELECT geckoid from gecko_categories");
      while ($obj2 = mysqli_fetch_assoc($rs)) {
          $our_data[] = $obj2;
      }

      




    $success = true;
    //$parameters = $data["parameters"];

    


    $curl = curl_init();

    curl_setopt_array($curl, array(
      CURLOPT_URL => 'https://pro-api.coingecko.com/api/v3/coins/categories?order=market_cap_desc',
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => '',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => 'GET',
      CURLOPT_HTTPHEADER => array(
        'Accept: application/json',
        'x-cg-pro-api-key: '.$gecko_api_key,
         ),
    ));
    
    $response = curl_exec($curl);
    
    curl_close($curl);

    $gecko_categories = json_decode($response, true);

    for($i =0; $i < sizeof($gecko_categories); $i++)
    {

        $gecko_id = $gecko_categories[$i]['id'];
        $name = $gecko_categories[$i]['name'];


        $is_existing_coin = false;
        for($s=0; $s<sizeof($our_data); $s++)
        {
            if(strcmp($our_data[$s]["geckoid"], $gecko_id) == 0 )
            {
                $is_existing_coin = true;
                break;
            }
        }


        if($is_existing_coin)
        {

            $call = mysqli_prepare($link, 'update gecko_categories set
            name=?, update_date=now() where geckoid = ?');
    
            mysqli_stmt_bind_param($call, 'ss', $name, $gecko_id );
            $rs = (mysqli_stmt_execute($call));
    
            if(!$rs)
            {
            $success = false;
            error_log("gecko_id=".$gecko_id,0);
            error_log(mysqli_error($link),0); 
    
            }




        }
        else
        {

            $call = mysqli_prepare($link, 'insert into gecko_categories 
            (geckoid, name, isactive, create_date, update_date ) 
            values (?, ?, 1, now(), now() )');
            mysqli_stmt_bind_param($call, 'ss', $gecko_id, $name );
            $rs = (mysqli_stmt_execute($call));
    
            if(!$rs)
            {
            $success = false;
            error_log("gecko_id=".$gecko_id,0);
            error_log(mysqli_error($link),0); 
    
            }

        }





     //   error_log("gecko_id=".$gecko_id,0);
     //   error_log("name=".$name,0);


  


     
     }

       //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" =>mysqli_error($link) );
        error_log( "Error details: " . mysqli_error($link) ,0);
    }



    echo json_encode($resp, JSON_PRETTY_PRINT);


    

}




function get_gecko_single_coin_by_id()
{
    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $data, $gecko_api_key,$is_debug;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='gecko_single'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'gecko_single', now() )");
    (mysqli_stmt_execute($call));



    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid from coindata3 order by id asc");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }


    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));



    $changed_count = 0;
    $new_count = 0;

    $curl = curl_init();


    for($i = 0; $i < sizeof($our_data); $i++ )
    {

        $coin_id = $our_data[$i]['geckoid'];

 

    $is_existing_coin = false;


    
    $call = mysqli_prepare($link, 'SELECT geckoid from coindata4 where geckoid=?');
    mysqli_stmt_bind_param($call, 's', $coin_id );
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);



    if ($rs) {
        //$row = mysqli_fetch_assoc($rs);
        $row_count = mysqli_num_rows($rs);

        //only 1 row must return for provided geckoid, no more no less.
        if ($row_count >= 1) {

            $is_existing_coin = true;
           


        }
        else
        {
            $is_existing_coin = false;
        }
        
    }
    else
    {
        $is_existing_coin = false;
    }
 


    
    $existing_categories = array();

    $call = mysqli_prepare($link, "SELECT * from coindata_categories where geckoid=?");
    mysqli_stmt_bind_param($call, 's', $coin_id );
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);

    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $existing_categories[] = $obj2;


    }




    $existing_exchanges = array();

    $call = mysqli_prepare($link, "SELECT * from coindata_exchanges where geckoid=?");
    mysqli_stmt_bind_param($call, 's', $coin_id );
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);

    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $existing_exchanges[] = $obj2;


    }



    


    $success = true;



    

    /*
    $parameters = $data["parameters"];
    $coin_id = $parameters['coin_id'];
    error_log($coin_id,0);
    */



        curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://pro-api.coingecko.com/api/v3/coins/'.$coin_id.'?localization=false&tickers=true',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'x-cg-pro-api-key: '.$gecko_api_key,
            'Cookie: __cf_bm=9cRKr6XH7sqSrsImIBUDGIRuEBcUxICCZUaXRIwOkTw-1726487484-*******-fsbHPTqgkrbttFS3owNwYMdBHKRTDBK1TLxKY5Gp5.Wv_bqJZKuMI6BkRmBnzEqkM2WWWQRm9HmuRb1R2McvDg'
        ),
        ));

        $response = curl_exec($curl);

        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if($httpcode != 200)
        {

            
            error_log("gecko_id=".$coin_id,0);
            error_log("Response = ".$response,0);
            curl_close($curl);
            continue;
    
        }



        $gecko_coin_data = json_decode($response, true);
        $web_slug = $gecko_coin_data['web_slug'];
        $portfolio_user_count = $gecko_coin_data['watchlist_portfolio_users'];
        $description = $gecko_coin_data['description']['en'];
        
        $market_cap_fdv_ratio = $gecko_coin_data['market_data']['market_cap_fdv_ratio'];
        $total_value_locked = $gecko_coin_data['market_data']['total_value_locked'];
        $mcap_to_tvl_ratio = $gecko_coin_data['market_data']['mcap_to_tvl_ratio'];
        $fdv_to_tvl_ratio = $gecko_coin_data['market_data']['fdv_to_tvl_ratio'];


        //$marketcap =  $gecko_coin_data['market_data']['market_cap']['usd'];

        $makretcap = isset($makretcap) ? $makretcap : NULL;
        $market_cap_fdv_ratio = isset($market_cap_fdv_ratio) ? $market_cap_fdv_ratio : NULL;
        $total_value_locked = isset($total_value_locked) ? $total_value_locked : NULL;
        $mcap_to_tvl_ratio = isset($mcap_to_tvl_ratio) ? $mcap_to_tvl_ratio : NULL;
        $fdv_to_tvl_ratio = isset($fdv_to_tvl_ratio) ? $fdv_to_tvl_ratio : NULL;
        

        //şimdilik kapattık bu veri cryptorankten geliyor diye bu talbodakini kullanamyacagız.
        /*
        if( $marketcap < 1000000)
        {
          
            
            $call = mysqli_prepare($link, 'update coindata
            set update_date=now(), isactive=0, marketcap=? where geckoid=?');
            mysqli_stmt_bind_param($call, 'is', 
            $marketcap, $coin_id, 
            );
            $rs = (mysqli_stmt_execute($call));
    


        }
            */

        $homepage = "";
        if(sizeof( $gecko_coin_data['links']['homepage'] ) > 0)
        $homepage = $gecko_coin_data['links']['homepage'][0];


        $chat_url_1 = "";
        $chat_url_2 = "";
        $chat_url_3 = "";

        if(sizeof( $gecko_coin_data['links']['chat_url'] ) > 2)
        {
            $chat_url_1 = $gecko_coin_data['links']['chat_url'][0];
            $chat_url_2 = $gecko_coin_data['links']['chat_url'][1];
            $chat_url_3 = $gecko_coin_data['links']['chat_url'][2];

        }
        else   if(sizeof( $gecko_coin_data['links']['chat_url'] ) > 1)
        {
            $chat_url_1 = $gecko_coin_data['links']['chat_url'][0];
            $chat_url_2 = $gecko_coin_data['links']['chat_url'][1];

        }
        else   if(sizeof( $gecko_coin_data['links']['chat_url'] ) > 0)
        {
            $chat_url_1 = $gecko_coin_data['links']['chat_url'][0];

        }

        $price_change_1d = $gecko_coin_data['market_data']['price_change_percentage_24h'];
        $price_change_7d = $gecko_coin_data['market_data']['price_change_percentage_7d'];
        $price_change_30d = $gecko_coin_data['market_data']['price_change_percentage_30d'];
        $price_change_60d = $gecko_coin_data['market_data']['price_change_percentage_60d'];
        $price_change_1y = $gecko_coin_data['market_data']['price_change_percentage_1y'];


        

        $portfolio_user_count = isset($portfolio_user_count) ? $portfolio_user_count : NULL;
        $description = isset($description) ? $description : NULL;
        $price_change_1d = isset($price_change_1d) ? $price_change_1d : NULL;
        $price_change_7d = isset($price_change_7d) ? $price_change_7d : NULL;
        $price_change_30d = isset($price_change_30d) ? $price_change_30d : NULL;
        $price_change_60d = isset($price_change_60d) ? $price_change_60d : NULL;
        $price_change_1y = isset($price_change_1y) ? $price_change_1y : NULL;
        




        if($is_existing_coin)
        {
            $changed_count++;

            $call = mysqli_prepare($link, 'update coindata4
            set web_slug=?, gecko_portfolio_count=?, description=?, homepage=?,
            chat_url_1=?, chat_url_2=?, chat_url_3=?,
            price_change_1d=round(?,2), price_change_7d=round(?,2), price_change_30d=round(?,2), price_change_60d=round(?,2), price_change_1y=round(?,2),
            market_cap_fdv_ratio=round(?,2), total_value_locked=round(?,2), mcap_to_tvl_ratio=round(?,2), fdv_to_tvl_ratio=round(?,2),
            update_date=now() where
            geckoid = ?');
          
            mysqli_stmt_bind_param($call, 'sissssssssssdddds',  
            $web_slug,
            $portfolio_user_count, $description, $homepage,
            $chat_url_1, $chat_url_2, $chat_url_3,
            $price_change_1d, $price_change_7d, $price_change_30d, $price_change_60d, $price_change_1y,
            $market_cap_fdv_ratio, $total_value_locked, $mcap_to_tvl_ratio, $fdv_to_tvl_ratio,
            $coin_id   );
            $rs = (mysqli_stmt_execute($call));
    
            if(!$rs)
            {
            $success = false;
            error_log("gecko_id=".$coin_id,0);
            error_log(mysqli_error($link),0); 
    
            }



        }
        else
        {

            $new_count++;

            $call = mysqli_prepare($link, 'insert into coindata4
            (geckoid, web_slug, gecko_portfolio_count, description, homepage, 
            chat_url_1, chat_url_2, chat_url_3, 
            price_change_1d, price_change_7d, price_change_30d, price_change_60d, price_change_1y,
            create_date, update_date,  market_cap_fdv_ratio, total_value_locked, mcap_to_tvl_ratio, fdv_to_tvl_ratio ) 
            values (?, ?, ?, ?, ?, ?, ?, ?, round(?,2), round(?,2), round(?,2), round(?,2), round(?,2), now(), now(), round(?,2), round(?,2), round(?,2), round(?,2) )');
            mysqli_stmt_bind_param($call, 'ssissssssssssdddd', 
            $coin_id, $web_slug, $portfolio_user_count, $description, $homepage,
            $chat_url_1, $chat_url_2, $chat_url_3,
            $price_change_1d, $price_change_7d, $price_change_30d, $price_change_60d, $price_change_1y,
            $market_cap_fdv_ratio, $total_value_locked, $mcap_to_tvl_ratio, $fdv_to_tvl_ratio
            );
            $rs = (mysqli_stmt_execute($call));
    
            if(!$rs)
            {
            $success = false;
            error_log("gecko_id=".$coin_id,0);
            error_log(mysqli_error($link),0); 
    
            }



        }


        $categories = $gecko_coin_data['categories'];

        for($k = 0; $k < sizeof($categories); $k++)
        {
            $category_name = $categories[$k];
            $category_name = isset($category_name) ? $category_name : NULL;



            $is_existing_category = false;
            for($s=0; $s<sizeof($existing_categories); $s++)
            {
                if(strcmp($existing_categories[$s]["category_name"], $category_name) == 0 )
                {
                    $is_existing_category = true;
                    break;
                }
            }
    
    
            if($is_existing_category)
            {
    
                $call = mysqli_prepare($link, 'update coindata_categories
                set update_date=now() where geckoid=? and category_name=?');
                mysqli_stmt_bind_param($call, 'ss', 
                $coin_id, $category_name
                );
                $rs = (mysqli_stmt_execute($call));
        
                if(!$rs)
                {
                $success = false;
                error_log("gecko_id=".$coin_id,0);
                error_log(mysqli_error($link),0); 
        
                }
    
    
    
    
            }
            else
            {
    
                $call = mysqli_prepare($link, 'insert into coindata_categories
                (geckoid, category_name, create_date, update_date  ) 
                values (?, ?, now(), now())');
                mysqli_stmt_bind_param($call, 'ss', 
                $coin_id, $category_name
                );
                $rs = (mysqli_stmt_execute($call));
        
                if(!$rs)
                {
                $success = false;
                error_log("gecko_id=".$coin_id,0);
                error_log(mysqli_error($link),0); 
        
                }



    
            }
    


        }




        $exchanges = $gecko_coin_data['tickers'];

        for($k = 0; $k < sizeof($exchanges); $k++)
        {
            $exchange = $exchanges[$k]['market']['identifier'];



            $is_existing_exchange = false;
            for($s=0; $s<sizeof($existing_exchanges); $s++)
            {
                if(strcmp($existing_exchanges[$s]["exchange_id"], $exchange) == 0 )
                {
                    $is_existing_exchange = true;
                    break;
                }
            }
    
    
            if($is_existing_exchange)
            {
    
                $call = mysqli_prepare($link, 'update coindata_exchanges
                set update_date=now() where geckoid=? and exchange_id=?');
                mysqli_stmt_bind_param($call, 'ss', 
                $coin_id, $exchange
                );
                $rs = (mysqli_stmt_execute($call));
        
                if(!$rs)
                {
                $success = false;
                error_log("gecko_id=".$coin_id,0);
                error_log(mysqli_error($link),0); 
        
                }
    
    
    
    
            }
            else
            {
    
                $call = mysqli_prepare($link, 'insert into coindata_exchanges
                (geckoid, exchange_id, create_date, update_date  ) 
                values (?, ?, now(), now())');
                mysqli_stmt_bind_param($call, 'ss', 
                $coin_id, $exchange
                );
                $rs = (mysqli_stmt_execute($call));
        
                if(!$rs)
                {
                $success = false;
                error_log("gecko_id=".$coin_id,0);
                error_log(mysqli_error($link),0); 
        
                }



    
            }






        }


        //find best ranked exchanges and listed exchange counts

        $call = mysqli_prepare($link, 'select count(*) as cex_count ,min(trust_score_rank) as best_score from gecko_exchanges where is_cex = 1 and geckoid in (select exchange_id from coindata_exchanges where geckoid = ? )');
         mysqli_stmt_bind_param($call, 's', $coin_id);
         (mysqli_stmt_execute($call));
         $rs = mysqli_stmt_get_result($call);

         $res = mysqli_fetch_assoc($rs);

         $cex_count = $res['cex_count'];
         $cex_best_rank = $res['best_score'];


         $call = mysqli_prepare($link, 'select count(*) as dex_count ,min(trust_score_rank) as best_score from gecko_exchanges where is_cex = 0 and geckoid in (select exchange_id from coindata_exchanges where geckoid = ? )');
         mysqli_stmt_bind_param($call, 's', $coin_id);
         (mysqli_stmt_execute($call));
         $rs = mysqli_stmt_get_result($call);

         $res = mysqli_fetch_assoc($rs);

         $dex_count = $res['dex_count'];
         $dex_best_rank = $res['best_score'];




         $call = mysqli_prepare($link, 'update coindata3 set best_cex_rank=?, best_dex_rank=?, cex_count=?, dex_count=? where geckoid=?');
         mysqli_stmt_bind_param($call, 'iiiis', $cex_best_rank, $dex_best_rank, $cex_count ,$dex_count,   $coin_id);
         (mysqli_stmt_execute($call));
         $rs = mysqli_stmt_get_result($call);


         /*
         error_log("cex_Count=".$cex_count,0);
         error_log("best_cex_score=".$cex_best_rank,0);
         error_log("dex_Count=".$dex_count,0);
         error_log("best_dex_score=".$dex_best_rank,0);
         */




        




        //500 request per minute limit
        usleep(120);

    

      //  if($i == 10)
      //  break;

      if (($i + 1)  %  (sizeof($our_data) / 100) == 0) {
        $progress = $i /  (sizeof($our_data) / 100);

        $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
        (mysqli_stmt_execute($call));
    }


    }

       

      

        

     //If No Error Occured Then Commit Changes To Db
     if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" =>mysqli_error($link) );
        error_log( "Error details: " . mysqli_error($link) ,0);
    }
        



    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='gecko_single'");
    (mysqli_stmt_execute($call));



    echo json_encode($resp, JSON_PRETTY_PRINT);



 curl_close($curl);





}


function get_gecko_exchanges()
{


    global $link, $gecko_api_key;


       //First get our platform data to compare if data needs to be updated or insert

       $our_data = array();
       $obj2 = array();
       $rs = mysqli_query($link, "SELECT geckoid from gecko_exchanges");
       while ($obj2 = mysqli_fetch_assoc($rs)) {
           $our_data[] = $obj2;
       }



       $success = true;


    $page = 1;


    for($k = 1; $k < 6; $k++)
    {


        $page = $k;


    $curl = curl_init();

    curl_setopt_array($curl, array(
    CURLOPT_URL => 'https://pro-api.coingecko.com/api/v3/exchanges?per_page=250&page='.$page,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'GET',
    CURLOPT_HTTPHEADER => array(
        'Accept: application/json',
        'x-cg-pro-api-key: '.$gecko_api_key,
        'Cookie: __cf_bm=q26sA.dOUE9wU2TGZBaOwHLk6psXSRZOjq8QTTMG_Ps-1726832476-*******-PwsgBVtpvzz0YGnNCMmCjuRI6ksJ6eRJcqySZtbuHOm9_UOXciylwKKwOaQs9VGjeR27bZ0xLzcXfJ708RFdYA'
    ),
    ));

    $response = curl_exec($curl);



    $gecko_exchanges = json_decode($response, true);

    for($i =0; $i < sizeof($gecko_exchanges); $i++)
    {
        $gecko_id = $gecko_exchanges[$i]['id'];
        $name = $gecko_exchanges[$i]['name'];
        $year = $gecko_exchanges[$i]['year_established'];
        $country = $gecko_exchanges[$i]['country'];
        $description = $gecko_exchanges[$i]['description'];
        $url = $gecko_exchanges[$i]['url'];
        $image = $gecko_exchanges[$i]['image'];
        $trust_score = $gecko_exchanges[$i]['trust_score'];
        $trust_score_rank = $gecko_exchanges[$i]['trust_score_rank'];
        $trade_volume = $gecko_exchanges[$i]['trade_volume_24h_btc'];
        $trade_volume_normalised = $gecko_exchanges[$i]['trade_volume_24h_btc_normalized'];


        $year = isset($year) ? $year : NULL;
        $country = isset($country) ? $country : NULL;
        $description = isset($description) ? $description : NULL;
        $url = isset($url) ? $url : NULL;
        $image = isset($image) ? $image : NULL;
        $trust_score = isset($trust_score) ? $trust_score : NULL;
        $trust_score_rank = isset($trust_score_rank) ? $trust_score_rank : NULL;
        $trade_volume = isset($trade_volume) ? $trade_volume : NULL;
        $trade_volume_normalised = isset($trade_volume_normalised) ? $trade_volume_normalised : NULL;



        $is_existing_coin = false;
        for($s=0; $s<sizeof($our_data); $s++)
        {
            if(strcmp($our_data[$s]["geckoid"], $gecko_id) == 0 )
            {
                $is_existing_coin = true;
                break;
            }
        }


        if($is_existing_coin)
        {

            $call = mysqli_prepare($link, 'update gecko_exchanges set
            name=?, year_established=?, country=?, description=?, url=?, image=?, trust_score=?,
            trust_score_rank=?, trade_volume_24h_btc=?, trade_volume_24h_btc_normalized=?, 
            update_date=now() where geckoid = ?');
    
            mysqli_stmt_bind_param($call, 'ssssssiidds', $name, $year, $country, $description, $uri, $image, $trust_score, $trust_score_rank, $trade_volume, $trade_volume_normalised, $gecko_id   );
            $rs = (mysqli_stmt_execute($call));
    
            if(!$rs)
            {
            $success = false;
            error_log("gecko_id=".$gecko_id,0);
            error_log(mysqli_error($link),0); 
    
            }





        }
        else
        {

           
        $call = mysqli_prepare($link, 'insert into gecko_exchanges 
        (geckoid, name, year_established, country, description, url, image, trust_score, trust_score_rank, trade_volume_24h_btc, trade_volume_24h_btc_normalized, create_date, update_date) 
        values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), now())');
        mysqli_stmt_bind_param($call, 'sssssssiidd',  $gecko_id, $name, $year, $country, $description, $uri, $image, $trust_score, $trust_score_rank, $trade_volume, $trade_volume_normalised   );
        $rs = (mysqli_stmt_execute($call));

        if(!$rs)
        {
        $success = false;
        error_log("gecko_id=".$gecko_id,0);
        error_log(mysqli_error($link),0); 
 
        }

        }




        


    }

    curl_close($curl);


     

   // echo $response;

    }


       //If No Error Occured Then Commit Changes To Db
       if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" =>mysqli_error($link) );
        error_log( "Error details: " . mysqli_error($link) ,0);
    }



    echo json_encode($resp, JSON_PRETTY_PRINT);

    






}


function update_gecko_exchange_extra_data()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);
    

    global $link, $gecko_api_key;

      //First get our platform data to compare if data needs to be updated or insert

      $our_data = array();
      $obj2 = array();
      $rs = mysqli_query($link, "SELECT geckoid from gecko_exchanges");
      while ($obj2 = mysqli_fetch_assoc($rs)) {
          $our_data[] = $obj2;
      }


      $success = true;

      $curl = curl_init();

      for($i=0; $i<sizeof($our_data); $i++)
      {
      


        $gecko_id = $our_data[$i]['geckoid'];


      
       

        curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://pro-api.coingecko.com/api/v3/exchanges/'.$gecko_id,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'x-cg-pro-api-key: '.$gecko_api_key
        
        ),
        ));

        $response = curl_exec($curl);


        $gecko_exchange_data = json_decode($response, true);


        

        if(isset($gecko_exchange_data['centralized']))
        {
        $is_cex = $gecko_exchange_data['centralized'];
        }
        else
        {
            $is_cex = isset($is_cex) ? $is_cex : NULL;
        }
      

        $cex_status = 0;

        if($is_cex == false)
        {
            $cex_status = 0;

        }
        else
        {
            $cex_status = 1;
        }


        

        $call = mysqli_prepare($link, 'update gecko_exchanges 
        set is_cex=? where geckoid = ?');
  
        mysqli_stmt_bind_param($call, 'is', $cex_status, $gecko_id   );
        $rs = (mysqli_stmt_execute($call));

        if(!$rs)
        {
        $success = false;
        error_log("gecko_id=".$gecko_id,0);
        error_log(mysqli_error($link),0); 
 
        }



       

        

      }




      curl_close($curl);



         //If No Error Occured Then Commit Changes To Db
         if ($success) {
            $resp = array("status" => "success");
        } else {
            $resp = array("status" => "fail", "details" =>mysqli_error($link) );
            error_log( "Error details: " . mysqli_error($link) ,0);
        }


        echo json_encode($resp, JSON_PRETTY_PRINT);

        


      



}

function calculate_scores_old()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);

    //First get all metrics and metric groups


    global $link,$is_debug;

    $isError = false;

    $call = mysqli_prepare($link, 'select * from metric_groups_all ');
   // mysqli_stmt_bind_param($call, 'i', $campaign_id);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);



    $metrics = array();
    if ($rs) {
       
        while ($obj = mysqli_fetch_assoc($rs)) {
        

            $metrics[] = $obj;

        }

        
    }
    else
    {

        $isError = true;
    }

    if($isError)
    {
        $debug_info = "";
        if($is_debug)
        $debug_info = "Something went wrong while getting campaign deposits.";
    


        http_response_code(404);
        echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
        error_log( "Error details: " . mysqli_error($link) ,0);
        die();

        
    }



      //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $metric_groups = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT * from metric_groups");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $metric_groups[] = $obj2;
    }


    




    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT * from coindata_all");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }



      //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $coin_scores = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid, metric_subgroup from coin_scores");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $coin_scores[] = $obj2;
    }

  
      //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $coin_group_scores = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid, metric_group from coin_group_scores");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $coin_group_scores[] = $obj2;
    }




//Now loop all coins and calculate scores 


    $success = true;


    for($k=0; $k< sizeof($our_data); $k++)
    {
        $geckoid = $our_data[$k]['geckoid'];


        $group_score_1 = 0;
        $group_score_2 = 0;
        $group_score_3 = 0;
        $group_score_4 = 0;
        $group_score_5 = 0;
        $group_score_6 = 0;
        $total_score = 0;


    for($i=0; $i < sizeof($metrics); $i++ )
    {



        $is_it_update = false;

        
        for($s=0; $s < sizeof($coin_scores); $s++)
        {
           
            if( ($geckoid == $coin_scores[$s]['geckoid'] ) && ($metrics[$i]['id'] ==  $coin_scores[$s]['metric_subgroup']) )
            {
            
                $is_it_update = true;
                array_splice($coin_scores, $s, 1);
                break;

             
            }
        }


      




        $metric_score = 0;
        

        //for demo purposes 
        //if group_id == 1 (Tokenomics) make all scores 75
        //if group_id == 2 (Vesting) make all scores 80
        //if group_id == 3 (Security) make all scores 85
        //if group_id == 4 (Socials) make all scores 70
        //if group_id == 5 (Market) make all scores 75
        //if group_id == 6 (Fundamentals) make all scores 90


        $group_id = $metrics[$i]['metric_group'];
        $metric_weight = $metrics[$i]['value_percent'];
        $metric_id = $metrics[$i]['id'];

        


        //Metrics

        // 1 - Market Cap / FDV Ratio
        if($metric_id == 1)
        {
            //burada fdv hesaplanamıyorsa score olarak 100 verelim.
            if($our_data[$k]['fdv'] == NULL)
            {
                $metric_score = 100;
            }
            else
            {

                $metric_score = $our_data[$k]['marketcap'] / $our_data[$k]['fdv'] * 100;

            }

            


        }

        //2 - Max Supply 
        else if ($metric_id == 2)
        {

            if($our_data[$k]['max_supply'] == NULL)
            {
                $metric_score = 59;

            }
            else
            {
                $metric_score = 100;
            }
           

        }

         //3 - Team Anonymity
         else if ($metric_id == 3)
         {
            //error_log("evet metric id 3",0);
            //error_log($our_data[$k]['team_status'],0);


            if($our_data[$k]['team_status'] == 0)
            {
                $metric_score = 100;

            }
            else if( ($our_data[$k]['team_status'] == 1) || ($our_data[$k]['team_status'] == 2) )
            {
                $metric_score = 50 ;
            }



         }

          //4 - CG Follower Count
          else if ($metric_id == 4)
          {
            $cg_followers = $our_data[$k]['gecko_portfolio_count'];

            if($cg_followers == NULL)
            $cg_followers = 0;

            if($cg_followers < 1000)
            {
                $metric_score = 49;
            }
            else if ($cg_followers < 3000)
            {
                $metric_score = 64;

            }
            else if ($cg_followers < 10000)
            {
                $metric_score = 74;

            }
            else if ($cg_followers < 30000)
            {
                $metric_score = 89;

            }
            else
            {
                $metric_score = 100;
            }


            
          }

            //5 - 24H Trade Volume
         else if ($metric_id == 5)
         {
       
            $total_volume = $our_data[$k]['total_volume'];


            if($total_volume < 100000)
            {
                $metric_score = 19;

            }
            else if($total_volume < 200000)
            {
                $metric_score = 39;

            }
            else if($total_volume < 500000)
            {
                $metric_score = 59;

            }
            else if($total_volume < 1000000)
            {
                $metric_score = 79;

            }
            else 
            {
                $metric_score = 100;

            }
         



         }



        else
        {
            $metric_score = 10;
        }




        

        
     
        if($group_id == 1  )
        {

          //  $metric_score = 76;

            $group_score_1 += ($metric_weight *  $metric_score / 100);

            


        }
        else if($group_id == 2)
        {

          //  $metric_score = 80;

            $group_score_2 += ($metric_weight *  $metric_score / 100);

        }
        else if($group_id == 3)
        {

          //  $metric_score = 85;

            $group_score_3 += ($metric_weight *  $metric_score / 100);

        }
        else if($group_id == 4)
        {

          //  $metric_score = 70;

            $group_score_4 += ($metric_weight *  $metric_score / 100);

        }
        else if($group_id == 5)
        {

          //  $metric_score = 75;

            $group_score_5 += ($metric_weight *  $metric_score / 100);

        }
        else if($group_id == 6)
        {

          //  $metric_score = 90;

            $group_score_6 += ($metric_weight *  $metric_score / 100);

        }
    


       

        if($is_it_update)
        {
          

            $call = mysqli_prepare($link, 'update coin_scores
            set score=?, update_date=now() where geckoid=? and metric_subgroup=?');
            mysqli_stmt_bind_param($call, 'isi', 
            $metric_score, $geckoid, $metric_id
            );
            $rs = (mysqli_stmt_execute($call));
    
            if(!$rs)
            {
            $success = false;
            error_log("gecko_id=".$geckoid,0);
            error_log(mysqli_error($link),0); 
    
            }




        }
        else
        {

            $call = mysqli_prepare($link, 'insert into coin_scores
            (geckoid, metric_subgroup, score  ) 
            values (?, ?, ?)');
            mysqli_stmt_bind_param($call, 'sii', 
            $geckoid, $metric_id, $metric_score
            );
            $rs = (mysqli_stmt_execute($call));
    
            if(!$rs)
            {
            $success = false;
            error_log("gecko_id=".$geckoid,0);
            error_log(mysqli_error($link),0); 
    
            }




        }









    }




    for($c=0; $c < sizeof($metric_groups); $c++ )
    {


    $is_it_update = false;
    $metric_group_id = $metric_groups[$c]['id'];
    $group_weight = $metric_groups[$c]['value'];

    $group_score = ${"group_score_".$metric_groups[$c]['id']};


    $total_score += $group_score * $group_weight / 100;

    for($s=0; $s < sizeof($coin_group_scores); $s++)
    {
        if($geckoid == $coin_group_scores[$s]['geckoid'] && $metric_groups[$c]['id'] ==  $coin_group_scores[$s]['metric_group'] )
        {
        
            $is_it_update = true;
            break;
        }
    }


    

    //error_log($group_score,0);

    if($is_it_update)
    {

        $call = mysqli_prepare($link, 'update coin_group_scores
        set score=round(?), update_date=now() where geckoid=? and metric_group=?');
        mysqli_stmt_bind_param($call, 'isi', 
        $group_score, $geckoid, $metric_group_id
        );
        $rs = (mysqli_stmt_execute($call));

        if(!$rs)
        {
        $success = false;
        error_log("gecko_id=".$geckoid,0);
        error_log(mysqli_error($link),0); 

        }




    }
    else
    {

        $call = mysqli_prepare($link, 'insert into coin_group_scores
        (geckoid, metric_group, score  ) 
        values (?, ?, round(?))');
        mysqli_stmt_bind_param($call, 'sii', 
        $geckoid, $metric_group_id, $group_score
        );
        $rs = (mysqli_stmt_execute($call));

        if(!$rs)
        {
        $success = false;
        error_log("gecko_id=".$geckoid,0);
        error_log(mysqli_error($link),0); 

        }




    }





    }  



    $call = mysqli_prepare($link, 'update coindata
    set total_score=round(?) where geckoid=? ');
    mysqli_stmt_bind_param($call, 'is', 
    $total_score, $geckoid
    );
    $rs = (mysqli_stmt_execute($call));

    if(!$rs)
    {
    $success = false;
    error_log("gecko_id=".$geckoid,0);
    error_log(mysqli_error($link),0); 

    }




  //  if($k == 10)
  //  break;
  

}



  //If No Error Occured Then Commit Changes To Db
  if ($success) {
    $resp = array("status" => "success");
} else {
    $resp = array("status" => "fail", "details" =>mysqli_error($link) );
    error_log( "Error details: " . mysqli_error($link) ,0);
}
   

echo json_encode($resp, JSON_PRETTY_PRINT);








}






















function getInitData()
{
   

    $initData = array();

    $initData['currencyList'] = generateCurrencyList();
    $initData['userTierList'] = generateUserTierList();

    echo json_encode($initData,JSON_PRETTY_PRINT);
    


}


function generateCurrencyList()
{


    global $link;


    $currency_array = array();
    $query = "select * from currency where isactive = 1";
    $rs = mysqli_query($link, $query);
    while ($obj = mysqli_fetch_assoc($rs)) {
        $currency_array[] = $obj;
    }



    $currency_detail_array = array();
    $query = "select * from currencyview";
    $rs = mysqli_query($link, $query);
    while ($obj = mysqli_fetch_assoc($rs)) {
        $currency_detail_array[] = $obj;  
    }


    //prepare currency & network grouped data
    $currencies = array();
    for($i = 0; $i < sizeof($currency_array); $i++)
    {

        $currency_row["currencyId"] = $currency_array[$i]['id'];
        $currency_row["currencyTitle"] = $currency_array[$i]['title'];
     


        $currency_networks = array();
        for($k = 0; $k < sizeof($currency_detail_array); $k++)
        {


            $is_network_exists = false;
            for($t = 0; $t < sizeof($currency_networks); $t++)
            {

              
 
                if($currency_networks[$t]['network_id'] == $currency_detail_array[$k]['cur_network'])
                {
                   
                    $is_network_exists = true;
                    break;

        
                }

            }

            if(!$is_network_exists)
            {

                if($currency_array[$i]['id'] == $currency_detail_array[$k]['currencyId'] )
                {
                $network_row["network_id"] =  $currency_detail_array[$k]['cur_network'];
                $network_row["network_title"] =  $currency_detail_array[$k]['net_title'];
                array_push($currency_networks,$network_row);
                }
            }


        }


        $currency_row["networks"] = $currency_networks;
        array_push($currencies,$currency_row);


    }





        //now add deposit data to currency, network grouped data


        for($i = 0; $i < sizeof($currencies); $i++)
        {
            $currency_networks = $currencies[$i]['networks'];


            
            for($k = 0; $k < sizeof($currency_networks); $k++)
            {

                $currency_deposits = array();
                for($s = 0; $s < sizeof($currency_detail_array); $s++)
                {
                    if($currency_detail_array[$s]['currencyId'] == $currencies[$i]['currencyId'] && $currency_detail_array[$s]['cur_network'] == $currency_networks[$k]['network_id'] )
                    {
                        $deposit_row['depositId'] = $currency_detail_array[$s]['cur_id'];
                        $deposit_row['depositTitle'] = $currency_detail_array[$s]['cur_title'];
                        $deposit_row["depositNetwork"] = $currency_detail_array[$s]['net_title'];

                        array_push($currency_deposits, $deposit_row);

                    }

                  
                }


                $currency_networks[$k]['deposits'] = $currency_deposits;


            }

            $currencies[$i]['networks'] = $currency_networks;
        }


    return $currencies;




}


function generateUserTierList()
{

    global $link;


    $userTierList_array = array();
    $query = "select id as userlist_id, title as userlist_title from userlist where isactive = 1";
    $rs = mysqli_query($link, $query);
    while ($obj = mysqli_fetch_assoc($rs)) {
        $userTierList_array[] = $obj;
    }

    return $userTierList_array;



}





//---------------------------------------------------------------------//
//Client Endpoints
//---------------------------------------------------------------------//

function getClientCampaignData()
{

    global $data, $link, $is_debug;

    $walletAddress = $data["walletAddress"];
    $affiliateLink = $data["affiliateLink"];


    if(is_null($affiliateLink) || $affiliateLink == "" || is_null($walletAddress) || $walletAddress == "")
    {
        $debug_info = "";
        if($is_debug)
        $debug_info = "Affiliate Link or WalletAddress is null or empty.";
    
    
    
        http_response_code(404);
        echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
        error_log( "Error details: " . mysqli_error($link) ,0);
        return;

    }


    //getCampaignId if AffiliateLink is valid 
    
    $campaign_data = getCampaignFromAffiliateLink($affiliateLink);


    //todo : find campagin's active rule for this walletaddress
    //for now make this static 37

   $rule =  getCampaignRuleFromCampaignId(43);
   $deposits = getCampaignDepositsFromCampaignId($campaign_data["id"]);

  
   $campaign["campaignId"] = $campaign_data["id"];
   $campaign["title"] = $campaign_data["title"];
   $campaign["description"] = $campaign_data["description"];
   $campaign["totalAmount"] = $campaign_data["amount"];
   $campaign["progress_percent"] = 56;
   $campaign["progress_amount"] = 561;
   $campaign["rule"] = $rule;
   $campaign["deposits"] = $deposits;



   echo json_encode($campaign, JSON_PRETTY_PRINT);



}

function getactiveCampaignData()
{

    global $data, $link, $is_debug;

    $isError = false;

    $call = mysqli_prepare($link, 'select campaign.*,campaignaffiliates.link  from campaign 
    left join campaignaffiliates on campaignaffiliates.campaign_id = campaign.id
    where campaign.isactive = 1');
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);



    $activeCampaigns = array();
    if ($rs) {
       
        while ($obj = mysqli_fetch_assoc($rs)) {
        

            $activeCampaigns[] = $obj;

        }

        
    }
    else
    {

        $isError = true;
    }

    if($isError)
    {
        $debug_info = "";
        if($is_debug)
        $debug_info = "Something went wrong while getting campaign list.";
    


        http_response_code(404);
        echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
        error_log( "Error details: " . mysqli_error($link) ,0);
        die();


        
    }


    $generatedCampaigns = array();

    for($i=0; $i < sizeof($activeCampaigns); $i++)
    {

    //getCampaignId if AffiliateLink is valid 
    
    $campaign_data = $activeCampaigns[$i];



   $campaign["campaignId"] = $campaign_data["id"];
   $campaign["title"] = $campaign_data["title"];
   $campaign["description"] = $campaign_data["description"];
   $campaign["totalAmount"] = $campaign_data["amount"];
   $campaign["link"] = $campaign_data["link"];
   $campaign["progress_percent"] = 56;
   $campaign["progress_amount"] = 561;


   array_push($generatedCampaigns,$campaign);


    }

   echo json_encode(array("campaignList" => $generatedCampaigns), JSON_PRETTY_PRINT);



}



function getCampaignFromAffiliateLink($affiliateLink)
{
    global $link,$is_debug;

    $isError = false;

    $call = mysqli_prepare($link, 'select * from campaign where id = (select campaign_id from campaignaffiliates where isactive=1 and link = ?)');
    mysqli_stmt_bind_param($call, 's', $affiliateLink);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);

    //if something went wrong then return -1 for invalid campaign affiliate link.
    if ($rs) {
        $row = mysqli_fetch_assoc($rs);
        $row_count = mysqli_num_rows($rs);

        //only 1 row must return for provided affiliatelink, no more no less.
        if ($row_count == 1) {

            return $row;


        }
        else
        {
            $isError = true;
        }
        
    }
    else
    {
        $isError = true;
      
    }


    if($isError)
    {

        $debug_info = "";
        if($is_debug)
        $debug_info = "No campaign exists for this affiliate link.";
    

        http_response_code(404);
        echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
        error_log( "Error details: " . mysqli_error($link) ,0);
        die();


    }



}



function getCampaignRuleFromCampaignId($rule_id)
{
    global $link,$is_debug;

    $isError = false;

    $call = mysqli_prepare($link, 'select * from campaignrules  where id = ? and isactive = 1');
    mysqli_stmt_bind_param($call, 'i', $rule_id);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);

    //if something went wrong then return -1 for invalid rule id.
    if ($rs) {
        $row = mysqli_fetch_assoc($rs);
        $row_count = mysqli_num_rows($rs);

        //only 1 row must return for provided affiliatelink, no more no less.
        if ($row_count == 1) {

            return $row;


        }
        else
        {
            $isError = true;
        }
        
    }
    else
    {

        $isError = true;
    }

    if($isError)
    {
        $debug_info = "";
        if($is_debug)
        $debug_info = "Campaign Rule Error, Campaign rule couldn't found";
    


        http_response_code(404);
        echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
        error_log( "Error details: " . mysqli_error($link) ,0);
        die();


        
    }

}


function getCampaignDepositsFromCampaignId($campaign_id)
{
    global $link,$is_debug;

    $isError = false;

    $call = mysqli_prepare($link, 'select * from campaigndepositview  where campaign_id = ?');
    mysqli_stmt_bind_param($call, 'i', $campaign_id);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);



    $deposits = array();
    if ($rs) {
       
        while ($obj = mysqli_fetch_assoc($rs)) {
        

            $deposits[] = $obj;

        }


        return $deposits;
        
    }
    else
    {

        $isError = true;
    }

    if($isError)
    {
        $debug_info = "";
        if($is_debug)
        $debug_info = "Something went wrong while getting campaign deposits.";
    


        http_response_code(404);
        echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
        error_log( "Error details: " . mysqli_error($link) ,0);
        die();


        
    }

}



//---------------------------------------------------------------------//
//Utility Functions                                                    //
//---------------------------------------------------------------------//
function cors2()
{
    // Allow from any origin
    if (isset($_SERVER['HTTP_ORIGIN'])) {
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');    // cache for 1 day
    }

    // Access-Control headers are received during OPTIONS requests
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
            // may also be using PUT, PATCH, HEAD etc
            header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        }

        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
            header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        }

        exit(0);
    }
}


function SYNC_GUID()
{
    if (function_exists('com_create_guid') === true) {
        return trim(com_create_guid(), '{}');
    }

    return sprintf('%04X%04X-%04X-%04X-%04X-%04X%04X%04X', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(16384, 20479), mt_rand(32768, 49151), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535));
}




function affiliateGuid()
{
    if (function_exists('com_create_guid') === true)
    {
        return trim(com_create_guid(), '{}');
    }

    return strtolower(sprintf('%04X%04X-%04X-%04X-%04X-%04X%04X%04X', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(16384, 20479), mt_rand(32768, 49151), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535)));
}











