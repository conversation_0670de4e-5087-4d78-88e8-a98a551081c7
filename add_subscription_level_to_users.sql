-- SQL script to add subscription_level column to users table
-- This ensures the subscription_level field exists for subscription management

-- Add subscription_level column if it doesn't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS subscription_level VARCHAR(50) DEFAULT 'free' AFTER email;

-- Update any NULL values to 'free'
UPDATE users SET subscription_level = 'free' WHERE subscription_level IS NULL;

-- Add index for better performance
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_subscription_level (subscription_level);

-- Add comments
-- subscription_level: User's subscription level (free, basic, advance, premium)
