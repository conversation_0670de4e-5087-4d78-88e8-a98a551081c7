<?php
/**
 * Email Services for CoinScout Backend
 *
 * This file contains methods for sending various types of emails to users and administrators.
 */

require_once 'utils.php';
require_once 'mailsender.php';
require_once('models/ResultModel.php');

/**
 * Report an error related to a specific coin
 *
 * @param string $coin_id The ID of the coin
 * @param string $coin_name The name of the coin
 * @param string $detail The error details
 * @param int $user_id The ID of the user reporting the error
 * @return void
 */
function report_error_v2($coin_id, $coin_name, $detail, $user_id)
{
    global $link;
    $coin_id = htmlspecialchars(trim($coin_id), ENT_QUOTES, 'UTF-8');
    $coin_name = htmlspecialchars(trim($coin_name), ENT_QUOTES, 'UTF-8');
    $detail = htmlspecialchars(trim($detail), ENT_QUOTES, 'UTF-8');
    $call = mysqli_prepare($link, 'INSERT INTO error_reports (coin_id, coin_name, detail, status, user_id, create_date) VALUES (?, ?, ?, "NEW", ?, NOW())');
    mysqli_stmt_bind_param($call, 'sssi', $coin_id, $coin_name, $detail, $user_id);
    $rs = mysqli_stmt_execute($call);
    if (!$rs) {
        error_log("Error saving report: " . mysqli_error($link), 0);
    }
    $body = "
<div style='display: flex; flex-direction: column; width: auto; border-width: 1px; border-style: solid; border-color: #334155; border-radius: 0.5rem; padding: 16px; background-color: #0f172a; color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif;'>
    <div style='padding-bottom: 12px; border-bottom: 1px solid #1e293b; margin-bottom: 12px;'>
        <p style='font-size: 20px; font-weight: 600; color: #f8fafc; margin: 0;'>Error Report</p>
    </div>
    <p style='font-size: 14px; font-weight: 500; color: #cbd5e1; margin: 6px 0;'><span style='color: #94a3b8;'>User ID:</span> $user_id</p>
    <p style='font-size: 14px; font-weight: 500; color: #cbd5e1; margin: 6px 0;'><span style='color: #94a3b8;'>Coin ID:</span> $coin_id</p>
    <p style='font-size: 14px; font-weight: 500; color: #cbd5e1; margin: 6px 0;'><span style='color: #94a3b8;'>Coin Name:</span> $coin_name</p>
    <div style='margin-top: 16px; padding: 12px; background-color: #1e293b; border-radius: 0.375rem;'>
        <p style='font-size: 14px; font-weight: 400; color: #f8fafc; margin: 0; line-height: 1.5;'>$detail</p>
    </div>
    <div style='margin-top: 20px; padding-top: 16px; border-top: 1px solid #1e293b;'>
        <p style='font-size: 12px; color: #94a3b8; margin: 0;'>Sent via <span style='color: #0ea5e9; font-weight: 500;'>CoinScout Error Report</span></p>
    </div>
</div>";
    send_mail($body, ["<EMAIL>"], "Coinscout Report Error Message");

    global $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $response = new SuccessResult($clientMessages[$lang]['request_submitted']);
    $response->send();
}

/**
 * Submit a feature request
 *
 * @param string $detail The feature request details
 * @param int $user_id The ID of the user submitting the request
 * @return void
 */
function request_feature($detail, $user_id)
{
    global $link;
    $detail = htmlspecialchars(trim($detail), ENT_QUOTES, 'UTF-8');
    $call = mysqli_prepare($link, 'INSERT INTO feature_requests (detail, status, user_id, create_date) VALUES (?, "NEW", ?, NOW())');
    mysqli_stmt_bind_param($call, 'si', $detail, $user_id);
    $rs = mysqli_stmt_execute($call);
    if (!$rs) {
        error_log("Error saving feature request: " . mysqli_error($link), 0);
    }
    $body = "
<div style='display: flex; flex-direction: column; width: auto; border-width: 1px; border-style: solid; border-color: #334155; border-radius: 0.5rem; padding: 16px; background-color: #0f172a; color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif;'>
    <div style='padding-bottom: 12px; border-bottom: 1px solid #1e293b; margin-bottom: 12px;'>
        <p style='font-size: 20px; font-weight: 600; color: #f8fafc; margin: 0 0 4px 0;'>Feature Request</p>
    </div>
    <p style='font-size: 14px; font-weight: 500; color: #cbd5e1; margin: 8px 0;'><span style='color: #94a3b8;'>User ID:</span> $user_id</p>
    <div style='margin-top: 16px; padding: 12px; background-color: #1e293b; border-radius: 0.375rem;'>
        <p style='font-size: 14px; font-weight: 400; color: #f8fafc; margin: 0; line-height: 1.5;'>$detail</p>
    </div>
    <div style='margin-top: 20px; padding-top: 16px; border-top: 1px solid #1e293b;'>
        <p style='font-size: 12px; color: #94a3b8; margin: 0;'>Sent via <span style='color: #0ea5e9; font-weight: 500;'>CoinScout Feature Request</span></p>
    </div>
</div>";
    send_mail($body, ["<EMAIL>"], "Coinscout Request Feature Message");

    global $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $response = new SuccessResult($clientMessages[$lang]['request_submitted']);
    $response->send();
}

/**
 * Submit a support request
 *
 * @param string $email The email of the user submitting the request
 * @param string $subject The subject of the support request
 * @param string $message The message content
 * @return void
 */
function contact_support_request($email, $subject, $message)
{
    global $link;
    $email = htmlspecialchars(trim($email), ENT_QUOTES, 'UTF-8');
    $subject = htmlspecialchars(trim($subject), ENT_QUOTES, 'UTF-8');
    $message = htmlspecialchars(trim($message), ENT_QUOTES, 'UTF-8');
    $call = mysqli_prepare($link, 'INSERT INTO support_requests (email, subject, message, status, create_date) VALUES (?, ?, ?, "OPEN", NOW())');
    mysqli_stmt_bind_param($call, 'sss', $email, $subject, $message);
    $rs = mysqli_stmt_execute($call);
    if (!$rs) {
        error_log("Error saving support request: " . mysqli_error($link), 0);
    }
    $body = "
<div style='display: flex; flex-direction: column; width: auto; border-width: 1px; border-style: solid; border-color: #334155; border-radius: 0.5rem; padding: 16px; background-color: #0f172a; color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif;'>
    <div style='padding-bottom: 12px; border-bottom: 1px solid #1e293b; margin-bottom: 12px;'>
        <p style='font-size: 20px; font-weight: 600; color: #f8fafc; margin: 0 0 4px 0;'>Support Message</p>
    </div>
    <p style='font-size: 14px; font-weight: 500; color: #cbd5e1; margin: 8px 0;'><span style='color: #94a3b8;'>Email:</span> $email</p>
    <p style='font-size: 14px; font-weight: 500; color: #cbd5e1; margin: 8px 0;'><span style='color: #94a3b8;'>Subject:</span> $subject</p>
    <div style='margin-top: 16px; padding: 12px; background-color: #1e293b; border-radius: 0.375rem;'>
        <p style='font-size: 14px; font-weight: 400; color: #f8fafc; margin: 0; line-height: 1.5;'>$message</p>
    </div>
    <div style='margin-top: 20px; padding-top: 16px; border-top: 1px solid #1e293b;'>
        <p style='font-size: 12px; color: #94a3b8; margin: 0;'>Sent via <span style='color: #0ea5e9; font-weight: 500;'>CoinScout Support</span></p>
    </div>
</div>";
    send_mail($body, ["<EMAIL>"], "Coinscout Support Contact Message");

    global $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $response = new SuccessResult($clientMessages[$lang]['request_submitted']);
    $response->send();
}
