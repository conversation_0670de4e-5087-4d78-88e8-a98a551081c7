# Güvenlik için PHP dosyalarının çalıştırılmasını engelle
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# Sadece belirli dosya türlerine izin ver
<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Order Allow,<PERSON>y
    Allow from all
</FilesMatch>

# <PERSON><PERSON>er tüm dosya türlerini engelle
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|webp)$).*$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Directory listing'i kapat
Options -Indexes

# Cache ayarları (opsiyonel)
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>
