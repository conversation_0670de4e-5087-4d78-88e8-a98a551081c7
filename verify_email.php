<?php
require_once 'vendor/autoload.php';
require_once 'email_verification.php';
require_once 'language_strings.php';
require_once 'language_config.php';
require_once 'config.php';

// Handle GET requests for email verification
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['token'])) {
    $token = $_GET['token'];
    $result = verify_email($token);
    
    // Redirect to appropriate page based on result
    if ($result['success']) {
        // Redirect to login page with success message
        header('Location: https://coinscout.app/login?verified=true');
    } else {
        // Redirect to error page
        header('Location: https://coinscout.app/verification-error');
    }
    exit;
}

// Handle direct access without token
header('Location: https://coinscout.app');
exit;
