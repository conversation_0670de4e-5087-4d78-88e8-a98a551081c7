<?php

ini_set('memory_limit', '-1');
set_time_limit(0);


require_once("/web/coinscout/api.php");
require_once("/web/coinscout/api2.php");


$query_txt = "select sync_status.isrunning as sync_status from sync_status 
where sync_type = 'sync_all' ";


$call = mysqli_prepare($link, $query_txt);
//mysqli_stmt_bind_param($call, 's', $coin_id);
(mysqli_stmt_execute($call));
$rs = mysqli_stmt_get_result($call);
$res = mysqli_fetch_assoc($rs);
$sync_status = $res['sync_status'];

if($sync_status == "true")
{
    echo "sync_all is already running...";
    return;
}





get_cr_coins();
get_gecko_coins();
get_lunarcrush_scores();
get_certik_security_scores();
calculate_scores();
