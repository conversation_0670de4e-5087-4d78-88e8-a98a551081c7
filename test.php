<?php

ini_set('memory_limit', '-1');
set_time_limit(0);

include_once('config.php');

global $link;




$rs = mysqli_query($link, "SELECT * from metric_subgroups where id = 2");
$all_data = [];
while ($obj2 = mysqli_fetch_assoc($rs)) {

$metric_id = $obj2['id'];
$encoded = $obj2['conclusion_text'];
$decoded = base64_decode($encoded);


$query_text = "update metric_subgroups set test_text=? where id=?";
 

$call = mysqli_prepare($link, $query_text );
mysqli_stmt_bind_param($call, 'si', $decoded, $metric_id);


$rs2 = (mysqli_stmt_execute($call));


if(!$rs2)
{
    echo $decoded;

    error_log(mysqli_error($link), 0);

}




}










?>