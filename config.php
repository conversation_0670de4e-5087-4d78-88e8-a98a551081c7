<?php

$gecko_api_key = 'CG-vYscxJ6sPqXVwCnGP4eWz1hr';
$cr_api_key = '397b887f8a4b466aae9a98e0aa261ae0127d5f318b6fd1b7508c8d728206';
$certik_api_key = 'abe3f236-67a9-4d40-9430-7c4e2bb3be56';
$lunarcrush_api_key = 'un7328mwr0duwxvq0a75ao9fcm4iiy643eof9yz';
$twitter_score_api = 'c93adb00bab9311f2acf330083419b94';

// JWT Token Configuration
define('JWT_ACCESS_TOKEN_DURATION_MINUTES', 30); // Access token süresi (dakika)

if (!defined('DB_SERVER')) {

   define('DB_SERVER', 'localhost:3306');
   define('DB_USERNAME', 'blockstart');
   define('DB_PASSWORD', 'Bs.2024**!');
   define('DB_DATABASE', 'coinscout');

}

   mysqli_report(MYSQLI_REPORT_OFF);
   $link = mysqli_connect(DB_SERVER,DB_USERNAME,DB_PASSWORD,DB_DATABASE);

/*mysqli_query($link,"SET NAMES 'utf8mb4'");
mysqli_query($link,"SET character_set_connection = 'utf8mb4'");
mysqli_query($link,"SET character_set_client = 'utf8mb4'");
mysqli_query($link,"SET character_set_results = 'utf8mb4'");
 */


?>
