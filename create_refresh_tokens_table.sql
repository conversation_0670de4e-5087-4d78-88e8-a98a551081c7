-- <PERSON><PERSON> script to create the refresh_tokens table

CREATE TABLE IF NOT EXISTS refresh_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires D<PERSON><PERSON><PERSON><PERSON> NOT NULL,
    revoked TINYINT(1) NOT NULL DEFAULT 0,
    reason_revoked VARCHAR(255) NULL,
    created_at DATETIME NOT NULL,
    created_by_ip VARCHAR(45) NULL,
    revoked_by_ip VARCHAR(45) NULL,
    INDEX (user_id),
    INDEX (token),
    INDEX (revoked),
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add comments to explain the table structure
-- id: Primary key
-- user_id: The ID of the user who owns the token
-- token: The refresh token string
-- expires: When the token expires
-- revoked: Whether the token has been revoked
-- reason_revoked: The reason the token was revoked
-- created_at: When the token was created
-- created_by_ip: The IP address that created the token
-- revoked_by_ip: The IP address that revoked the token
