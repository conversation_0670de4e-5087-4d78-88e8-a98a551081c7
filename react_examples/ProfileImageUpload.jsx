import React, { useState, useRef } from 'react';

const ProfileImageUpload = ({ currentImageUrl, onImageUpdate }) => {
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(currentImageUrl);
  const fileInputRef = useRef(null);

  // Dosya seçildiğinde çalışır
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Dosya türü kontrolü
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        alert('Sadece JPEG, PNG, GIF ve WebP formatları desteklenmektedir.');
        return;
      }

      // Dosya boyutu kontrolü (5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Dosya boyutu 5MB\'dan küçük olmalıdır.');
        return;
      }

      // Önizleme için URL oluştur
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target.result);
      };
      reader.readAsDataURL(file);

      // Dosyayı yükle
      uploadImage(file);
    }
  };

  // Profil fotoğrafını yükle
  const uploadImage = async (file) => {
    setUploading(true);
    
    try {
      const formData = new FormData();
      formData.append('profile_image', file);
      formData.append('action', 'upload');

      const token = localStorage.getItem('authToken'); // JWT token'ı al
      
      const response = await fetch('/upload_profile_image.php', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Başarılı yükleme
        const imageUrl = result.data.image_url;
        setPreviewUrl(imageUrl);
        onImageUpdate && onImageUpdate(imageUrl);
        alert('Profil fotoğrafı başarıyla yüklendi!');
      } else {
        // Hata durumu
        alert(result.message || 'Yükleme sırasında bir hata oluştu.');
        setPreviewUrl(currentImageUrl); // Eski fotoğrafa geri dön
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Yükleme sırasında bir hata oluştu.');
      setPreviewUrl(currentImageUrl); // Eski fotoğrafa geri dön
    } finally {
      setUploading(false);
    }
  };

  // Profil fotoğrafını sil
  const deleteImage = async () => {
    if (!window.confirm('Profil fotoğrafınızı silmek istediğinizden emin misiniz?')) {
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      formData.append('action', 'delete');

      const token = localStorage.getItem('authToken');
      
      const response = await fetch('/upload_profile_image.php', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setPreviewUrl(null);
        onImageUpdate && onImageUpdate(null);
        alert('Profil fotoğrafı başarıyla silindi!');
      } else {
        alert(result.message || 'Silme sırasında bir hata oluştu.');
      }
    } catch (error) {
      console.error('Delete error:', error);
      alert('Silme sırasında bir hata oluştu.');
    } finally {
      setUploading(false);
    }
  };

  // Dosya seçici aç
  const openFileSelector = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="profile-image-upload">
      <div className="image-container">
        {previewUrl ? (
          <img 
            src={previewUrl} 
            alt="Profile" 
            className="profile-image"
            style={{
              width: '150px',
              height: '150px',
              borderRadius: '50%',
              objectFit: 'cover',
              border: '3px solid #ddd'
            }}
          />
        ) : (
          <div 
            className="no-image-placeholder"
            style={{
              width: '150px',
              height: '150px',
              borderRadius: '50%',
              backgroundColor: '#f0f0f0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '3px solid #ddd',
              color: '#666'
            }}
          >
            Fotoğraf Yok
          </div>
        )}
      </div>

      <div className="upload-controls" style={{ marginTop: '15px' }}>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileSelect}
          accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
          style={{ display: 'none' }}
        />
        
        <button 
          onClick={openFileSelector}
          disabled={uploading}
          style={{
            padding: '10px 20px',
            marginRight: '10px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: uploading ? 'not-allowed' : 'pointer'
          }}
        >
          {uploading ? 'Yükleniyor...' : 'Fotoğraf Seç'}
        </button>

        {previewUrl && (
          <button 
            onClick={deleteImage}
            disabled={uploading}
            style={{
              padding: '10px 20px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: uploading ? 'not-allowed' : 'pointer'
            }}
          >
            {uploading ? 'Siliniyor...' : 'Fotoğrafı Sil'}
          </button>
        )}
      </div>

      <div className="upload-info" style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
        <p>• Maksimum dosya boyutu: 5MB</p>
        <p>• Desteklenen formatlar: JPEG, PNG, GIF, WebP</p>
      </div>
    </div>
  );
};

export default ProfileImageUpload;
