<?php

ini_set('memory_limit', '-1');
set_time_limit(0);


require_once("/web/coinscout/api.php");
require_once("/web/coinscout/api2.php");


$call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='sync_all'");
(mysqli_stmt_execute($call));


get_cr_coins();
get_gecko_coins();
get_lunarcrush_scores();
get_certik_security_scores();
get_cr_single_coin_by_id();
get_gecko_single_coin_by_id();
get_cr_vesting();
get_cr_funds();
get_cr_category();
get_cr_tags();
calculate_scores();

get_cr_publicsales();
get_cr_publicsales_extra();
get_twitter_scores();
get_twitter_followers();
get_twitter_best_followers();
calculate_ico_scores();

get_sparkline_history();



$call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='sync_all'");
(mysqli_stmt_execute($call));




?>