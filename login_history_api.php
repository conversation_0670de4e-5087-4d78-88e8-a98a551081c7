<?php
/**
 * Login History API
 * Provides endpoints for retrieving user login history and statistics
 */

require_once 'utils.php';
require_once 'models/ResultModel.php';
require_once 'login_tracking.php';

// Apply CORS headers
cors_client();

// Include database connection
include_once('config.php');

// Get the request payload
$payload = file_get_contents('php://input');
$data = json_decode($payload, true);

if ($data == null) {
    http_response_code(400);
    echo json_encode(['error' => "Invalid Request!", 'detail' => "Payload is not a valid json or null"]);
    return;
}

$functName = "";
if (isset($data['f'])) {
    $functName = $data['f'];
} else {
    http_response_code(400);
    echo json_encode(['error' => "Invalid Request!", 'detail' => "Endpoint variable is not set"]);
    return;
}

$validEndpoints = [
    "get_login_history",
    "get_login_stats",
    "get_recent_logins",
    "get_failed_attempts"
];

if (in_array($functName, $validEndpoints)) {
    // Authenticate user first
    $userId = authenticate_user();
    if (!$userId) {
        $response = new ErrorResult('Unauthorized access');
        $response->send(401);
        return;
    }

    if ($functName == "get_login_history") {
        $limit = isset($data['limit']) ? intval($data['limit']) : 20;
        $days = isset($data['days']) ? intval($data['days']) : 30;
        get_login_history($userId, $limit, $days);
    } else if ($functName == "get_login_stats") {
        $days = isset($data['days']) ? intval($data['days']) : 30;
        get_login_stats($userId, $days);
    } else if ($functName == "get_recent_logins") {
        $limit = isset($data['limit']) ? intval($data['limit']) : 10;
        get_recent_logins($userId, $limit);
    } else if ($functName == "get_failed_attempts") {
        $days = isset($data['days']) ? intval($data['days']) : 7;
        get_failed_attempts($userId, $days);
    }
} else {
    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => "Endpoint Not Exists"]);
}

/**
 * Get user login history
 */
function get_login_history($userId, $limit = 20, $days = 30) {
    $history = getUserLoginHistory($userId, $limit, $days);
    
    $response = new SuccessResult([
        'login_history' => $history,
        'total_records' => count($history),
        'period_days' => $days
    ]);
    $response->send();
}

/**
 * Get login statistics for a user
 */
function get_login_stats($userId, $days = 30) {
    global $link;
    
    // Get total logins
    $query = "SELECT 
        COUNT(*) as total_logins,
        COUNT(CASE WHEN success = 1 THEN 1 END) as successful_logins,
        COUNT(CASE WHEN success = 0 THEN 1 END) as failed_logins,
        COUNT(DISTINCT login_type) as login_methods_used,
        COUNT(DISTINCT ip_address) as unique_ips,
        COUNT(DISTINCT device_type) as device_types_used
    FROM user_login_history 
    WHERE user_id = ? 
    AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
    
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "ii", $userId, $days);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $stats = mysqli_fetch_assoc($result);
    
    // Get login methods breakdown
    $query = "SELECT 
        login_type,
        COUNT(*) as count,
        COUNT(CASE WHEN success = 1 THEN 1 END) as successful_count
    FROM user_login_history 
    WHERE user_id = ? 
    AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    GROUP BY login_type
    ORDER BY count DESC";
    
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "ii", $userId, $days);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $login_methods = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $login_methods[] = $row;
    }
    
    // Get daily login activity
    $query = "SELECT 
        DATE(created_at) as login_date,
        COUNT(*) as total_attempts,
        COUNT(CASE WHEN success = 1 THEN 1 END) as successful_attempts
    FROM user_login_history 
    WHERE user_id = ? 
    AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    GROUP BY DATE(created_at)
    ORDER BY login_date DESC";
    
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "ii", $userId, $days);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $daily_activity = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $daily_activity[] = $row;
    }
    
    $response = new SuccessResult([
        'stats' => $stats,
        'login_methods' => $login_methods,
        'daily_activity' => $daily_activity,
        'period_days' => $days
    ]);
    $response->send();
}

/**
 * Get recent successful logins
 */
function get_recent_logins($userId, $limit = 10) {
    global $link;
    
    $query = "SELECT 
        login_type,
        ip_address,
        device_type,
        browser,
        os,
        country,
        city,
        created_at
    FROM user_login_history 
    WHERE user_id = ? 
    AND success = 1
    ORDER BY created_at DESC 
    LIMIT ?";
    
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "ii", $userId, $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $recent_logins = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $recent_logins[] = $row;
    }
    
    $response = new SuccessResult([
        'recent_logins' => $recent_logins,
        'total_records' => count($recent_logins)
    ]);
    $response->send();
}

/**
 * Get failed login attempts
 */
function get_failed_attempts($userId, $days = 7) {
    global $link;
    
    $query = "SELECT 
        login_type,
        ip_address,
        device_type,
        browser,
        os,
        failure_reason,
        created_at
    FROM user_login_history 
    WHERE user_id = ? 
    AND success = 0
    AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    ORDER BY created_at DESC";
    
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "ii", $userId, $days);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $failed_attempts = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $failed_attempts[] = $row;
    }
    
    // Get summary stats for failed attempts
    $query = "SELECT 
        COUNT(*) as total_failed_attempts,
        COUNT(DISTINCT ip_address) as unique_ips,
        COUNT(DISTINCT login_type) as login_methods
    FROM user_login_history 
    WHERE user_id = ? 
    AND success = 0
    AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
    
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "ii", $userId, $days);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $summary = mysqli_fetch_assoc($result);
    
    $response = new SuccessResult([
        'failed_attempts' => $failed_attempts,
        'summary' => $summary,
        'period_days' => $days
    ]);
    $response->send();
}
?>
